[2025-07-23 19:33:58] ================================================================================
[2025-07-23 19:33:58] 加载量子态: L=4, J2=0.00, J1=0.05
[2025-07-23 19:33:58] 设置样本数为: 1048576
[2025-07-23 19:33:58] 开始生成共享样本集...
[2025-07-23 19:35:08] 样本生成完成,耗时: 70.535 秒
[2025-07-23 19:35:08] ================================================================================
[2025-07-23 19:35:08] 开始计算自旋结构因子...
[2025-07-23 19:35:08] 初始化操作符缓存...
[2025-07-23 19:35:08] 预构建所有自旋相关操作符...
[2025-07-23 19:35:08] 开始计算自旋相关函数...
[2025-07-23 19:35:17] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 8.621s
[2025-07-23 19:35:26] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 9.201s
[2025-07-23 19:35:30] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 3.604s
[2025-07-23 19:35:33] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 3.603s
[2025-07-23 19:35:37] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 3.603s
[2025-07-23 19:35:41] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 3.600s
[2025-07-23 19:35:44] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 3.602s
[2025-07-23 19:35:48] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 3.599s
[2025-07-23 19:35:51] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 3.582s
[2025-07-23 19:35:55] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 3.603s
[2025-07-23 19:35:59] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 3.604s
[2025-07-23 19:36:02] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 3.632s
[2025-07-23 19:36:06] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 3.602s
[2025-07-23 19:36:09] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 3.581s
[2025-07-23 19:36:13] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 3.602s
[2025-07-23 19:36:17] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 3.602s
[2025-07-23 19:36:20] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 3.602s
[2025-07-23 19:36:24] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 3.581s
[2025-07-23 19:36:27] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 3.617s
[2025-07-23 19:36:31] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 3.599s
[2025-07-23 19:36:35] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 3.601s
[2025-07-23 19:36:38] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 3.602s
[2025-07-23 19:36:42] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 3.602s
[2025-07-23 19:36:45] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 3.582s
[2025-07-23 19:36:49] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 3.602s
[2025-07-23 19:36:53] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 3.603s
[2025-07-23 19:36:56] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 3.601s
[2025-07-23 19:37:00] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 3.602s
[2025-07-23 19:37:03] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 3.583s
[2025-07-23 19:37:07] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 3.583s
[2025-07-23 19:37:11] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 3.602s
[2025-07-23 19:37:14] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 3.600s
[2025-07-23 19:37:18] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 3.602s
[2025-07-23 19:37:21] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 3.602s
[2025-07-23 19:37:25] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 3.582s
[2025-07-23 19:37:29] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 3.602s
[2025-07-23 19:37:32] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 3.627s
[2025-07-23 19:37:36] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 3.601s
[2025-07-23 19:37:39] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 3.602s
[2025-07-23 19:37:43] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 3.581s
[2025-07-23 19:37:47] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 3.602s
[2025-07-23 19:37:50] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 3.602s
[2025-07-23 19:37:54] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 3.602s
[2025-07-23 19:37:57] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 3.606s
[2025-07-23 19:38:01] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 3.599s
[2025-07-23 19:38:05] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 3.597s
[2025-07-23 19:38:08] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 3.582s
[2025-07-23 19:38:12] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 3.602s
[2025-07-23 19:38:15] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 3.581s
[2025-07-23 19:38:19] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 3.602s
[2025-07-23 19:38:23] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 3.602s
[2025-07-23 19:38:26] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 3.602s
[2025-07-23 19:38:30] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 3.602s
[2025-07-23 19:38:33] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 3.597s
[2025-07-23 19:38:37] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 3.582s
[2025-07-23 19:38:41] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 3.602s
[2025-07-23 19:38:44] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 3.601s
[2025-07-23 19:38:48] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 3.582s
[2025-07-23 19:38:51] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 3.603s
[2025-07-23 19:38:55] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 3.583s
[2025-07-23 19:38:59] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 3.601s
[2025-07-23 19:39:02] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 3.655s
[2025-07-23 19:39:06] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 3.603s
[2025-07-23 19:39:09] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 3.582s
[2025-07-23 19:39:09] 自旋相关函数计算完成,总耗时 241.06 秒
[2025-07-23 19:39:10] 计算傅里叶变换...
[2025-07-23 19:39:10] 自旋结构因子计算完成
[2025-07-23 19:39:11] 自旋相关函数平均误差: 0.000654
[2025-07-23 19:39:11] ================================================================================
[2025-07-23 19:39:11] 开始计算二聚体结构因子...
[2025-07-23 19:39:11] 识别x和y方向的二聚体...
[2025-07-23 19:39:11] 找到 64 个x方向二聚体和 64 个y方向二聚体
[2025-07-23 19:39:11] 预计算二聚体操作符...
[2025-07-23 19:39:12] 使用x-二聚体 (0, 1) (全局索引 0) 作为参考点, 计算x方向连通二聚体相关函数...
[2025-07-23 19:39:16] x方向算符进度: 1/64, 当前算符: D_(0, 1) * D_(0, 1), 耗时: 3.632s
[2025-07-23 19:39:30] x方向算符进度: 2/64, 当前算符: D_(0, 1) * D_(0, 49), 耗时: 14.717s
[2025-07-23 19:39:35] x方向算符进度: 3/64, 当前算符: D_(0, 1) * D_(1, 16), 耗时: 4.747s
[2025-07-23 19:39:47] x方向算符进度: 4/64, 当前算符: D_(0, 1) * D_(2, 3), 耗时: 11.672s
[2025-07-23 19:39:53] x方向算符进度: 5/64, 当前算符: D_(0, 1) * D_(2, 19), 耗时: 6.020s
[2025-07-23 19:39:59] x方向算符进度: 6/64, 当前算符: D_(0, 1) * D_(3, 50), 耗时: 6.021s
[2025-07-23 19:40:05] x方向算符进度: 7/64, 当前算符: D_(0, 1) * D_(4, 5), 耗时: 6.018s
[2025-07-23 19:40:11] x方向算符进度: 8/64, 当前算符: D_(0, 1) * D_(4, 53), 耗时: 5.979s
[2025-07-23 19:40:17] x方向算符进度: 9/64, 当前算符: D_(0, 1) * D_(5, 20), 耗时: 6.020s
[2025-07-23 19:40:23] x方向算符进度: 10/64, 当前算符: D_(0, 1) * D_(6, 7), 耗时: 6.017s
[2025-07-23 19:40:29] x方向算符进度: 11/64, 当前算符: D_(0, 1) * D_(6, 23), 耗时: 6.020s
[2025-07-23 19:40:35] x方向算符进度: 12/64, 当前算符: D_(0, 1) * D_(7, 54), 耗时: 6.019s
[2025-07-23 19:40:41] x方向算符进度: 13/64, 当前算符: D_(0, 1) * D_(8, 9), 耗时: 6.019s
[2025-07-23 19:40:47] x方向算符进度: 14/64, 当前算符: D_(0, 1) * D_(8, 57), 耗时: 5.978s
[2025-07-23 19:40:53] x方向算符进度: 15/64, 当前算符: D_(0, 1) * D_(9, 24), 耗时: 6.019s
[2025-07-23 19:40:59] x方向算符进度: 16/64, 当前算符: D_(0, 1) * D_(10, 11), 耗时: 6.021s
[2025-07-23 19:41:05] x方向算符进度: 17/64, 当前算符: D_(0, 1) * D_(10, 27), 耗时: 6.018s
[2025-07-23 19:41:11] x方向算符进度: 18/64, 当前算符: D_(0, 1) * D_(11, 58), 耗时: 5.979s
[2025-07-23 19:41:17] x方向算符进度: 19/64, 当前算符: D_(0, 1) * D_(12, 13), 耗时: 6.017s
[2025-07-23 19:41:23] x方向算符进度: 20/64, 当前算符: D_(0, 1) * D_(12, 61), 耗时: 5.978s
[2025-07-23 19:41:29] x方向算符进度: 21/64, 当前算符: D_(0, 1) * D_(13, 28), 耗时: 6.019s
[2025-07-23 19:41:35] x方向算符进度: 22/64, 当前算符: D_(0, 1) * D_(14, 15), 耗时: 6.018s
[2025-07-23 19:41:41] x方向算符进度: 23/64, 当前算符: D_(0, 1) * D_(14, 31), 耗时: 6.019s
[2025-07-23 19:41:47] x方向算符进度: 24/64, 当前算符: D_(0, 1) * D_(15, 62), 耗时: 5.978s
[2025-07-23 19:41:53] x方向算符进度: 25/64, 当前算符: D_(0, 1) * D_(16, 17), 耗时: 6.018s
[2025-07-23 19:41:59] x方向算符进度: 26/64, 当前算符: D_(0, 1) * D_(17, 32), 耗时: 6.016s
[2025-07-23 19:42:05] x方向算符进度: 27/64, 当前算符: D_(0, 1) * D_(18, 19), 耗时: 6.020s
[2025-07-23 19:42:11] x方向算符进度: 28/64, 当前算符: D_(0, 1) * D_(18, 35), 耗时: 6.016s
[2025-07-23 19:42:17] x方向算符进度: 29/64, 当前算符: D_(0, 1) * D_(20, 21), 耗时: 6.017s
[2025-07-23 19:42:23] x方向算符进度: 30/64, 当前算符: D_(0, 1) * D_(21, 36), 耗时: 5.978s
[2025-07-23 19:42:29] x方向算符进度: 31/64, 当前算符: D_(0, 1) * D_(22, 23), 耗时: 6.017s
[2025-07-23 19:42:35] x方向算符进度: 32/64, 当前算符: D_(0, 1) * D_(22, 39), 耗时: 6.019s
[2025-07-23 19:42:41] x方向算符进度: 33/64, 当前算符: D_(0, 1) * D_(24, 25), 耗时: 6.016s
[2025-07-23 19:42:47] x方向算符进度: 34/64, 当前算符: D_(0, 1) * D_(25, 40), 耗时: 6.017s
[2025-07-23 19:42:53] x方向算符进度: 35/64, 当前算符: D_(0, 1) * D_(26, 27), 耗时: 6.011s
[2025-07-23 19:42:59] x方向算符进度: 36/64, 当前算符: D_(0, 1) * D_(26, 43), 耗时: 5.978s
[2025-07-23 19:43:05] x方向算符进度: 37/64, 当前算符: D_(0, 1) * D_(28, 29), 耗时: 6.019s
[2025-07-23 19:43:11] x方向算符进度: 38/64, 当前算符: D_(0, 1) * D_(29, 44), 耗时: 6.017s
[2025-07-23 19:43:17] x方向算符进度: 39/64, 当前算符: D_(0, 1) * D_(30, 31), 耗时: 6.018s
[2025-07-23 19:43:23] x方向算符进度: 40/64, 当前算符: D_(0, 1) * D_(30, 47), 耗时: 5.978s
[2025-07-23 19:43:29] x方向算符进度: 41/64, 当前算符: D_(0, 1) * D_(32, 33), 耗时: 6.018s
[2025-07-23 19:43:35] x方向算符进度: 42/64, 当前算符: D_(0, 1) * D_(33, 48), 耗时: 6.016s
[2025-07-23 19:43:41] x方向算符进度: 43/64, 当前算符: D_(0, 1) * D_(34, 35), 耗时: 6.016s
[2025-07-23 19:43:47] x方向算符进度: 44/64, 当前算符: D_(0, 1) * D_(34, 51), 耗时: 6.016s
[2025-07-23 19:43:53] x方向算符进度: 45/64, 当前算符: D_(0, 1) * D_(36, 37), 耗时: 6.019s
[2025-07-23 19:43:59] x方向算符进度: 46/64, 当前算符: D_(0, 1) * D_(37, 52), 耗时: 5.979s
[2025-07-23 19:44:05] x方向算符进度: 47/64, 当前算符: D_(0, 1) * D_(38, 39), 耗时: 5.978s
[2025-07-23 19:44:11] x方向算符进度: 48/64, 当前算符: D_(0, 1) * D_(38, 55), 耗时: 6.010s
[2025-07-23 19:44:17] x方向算符进度: 49/64, 当前算符: D_(0, 1) * D_(40, 41), 耗时: 5.978s
[2025-07-23 19:44:23] x方向算符进度: 50/64, 当前算符: D_(0, 1) * D_(41, 56), 耗时: 6.018s
[2025-07-23 19:44:29] x方向算符进度: 51/64, 当前算符: D_(0, 1) * D_(42, 43), 耗时: 6.015s
[2025-07-23 19:44:35] x方向算符进度: 52/64, 当前算符: D_(0, 1) * D_(42, 59), 耗时: 6.017s
[2025-07-23 19:44:41] x方向算符进度: 53/64, 当前算符: D_(0, 1) * D_(44, 45), 耗时: 6.035s
[2025-07-23 19:44:47] x方向算符进度: 54/64, 当前算符: D_(0, 1) * D_(45, 60), 耗时: 5.978s
[2025-07-23 19:44:53] x方向算符进度: 55/64, 当前算符: D_(0, 1) * D_(46, 47), 耗时: 6.019s
[2025-07-23 19:44:59] x方向算符进度: 56/64, 当前算符: D_(0, 1) * D_(46, 63), 耗时: 6.017s
[2025-07-23 19:45:05] x方向算符进度: 57/64, 当前算符: D_(0, 1) * D_(48, 49), 耗时: 6.018s
[2025-07-23 19:45:11] x方向算符进度: 58/64, 当前算符: D_(0, 1) * D_(50, 51), 耗时: 5.978s
[2025-07-23 19:45:17] x方向算符进度: 59/64, 当前算符: D_(0, 1) * D_(52, 53), 耗时: 6.024s
[2025-07-23 19:45:23] x方向算符进度: 60/64, 当前算符: D_(0, 1) * D_(54, 55), 耗时: 5.977s
[2025-07-23 19:45:29] x方向算符进度: 61/64, 当前算符: D_(0, 1) * D_(56, 57), 耗时: 6.020s
[2025-07-23 19:45:35] x方向算符进度: 62/64, 当前算符: D_(0, 1) * D_(58, 59), 耗时: 6.015s
[2025-07-23 19:45:41] x方向算符进度: 63/64, 当前算符: D_(0, 1) * D_(60, 61), 耗时: 5.979s
[2025-07-23 19:45:47] x方向算符进度: 64/64, 当前算符: D_(0, 1) * D_(62, 63), 耗时: 6.018s
[2025-07-23 19:45:47] x方向二聚体相关函数计算完成,耗时: 395.34 秒
[2025-07-23 19:45:47] --------------------------------------------------------------------------------
[2025-07-23 19:45:47] 使用y-二聚体 (0, 3) (全局索引 64) 作为参考点, 计算y方向连通二聚体相关函数...
[2025-07-23 19:45:51] y方向算符进度: 1/64, 当前算符: D_(0, 3) * D_(0, 3), 耗时: 3.599s
[2025-07-23 19:45:56] y方向算符进度: 2/64, 当前算符: D_(0, 3) * D_(0, 15), 耗时: 4.732s
[2025-07-23 19:46:02] y方向算符进度: 3/64, 当前算符: D_(0, 3) * D_(1, 2), 耗时: 6.023s
[2025-07-23 19:46:08] y方向算符进度: 4/64, 当前算符: D_(0, 3) * D_(1, 14), 耗时: 6.025s
[2025-07-23 19:46:14] y方向算符进度: 5/64, 当前算符: D_(0, 3) * D_(2, 5), 耗时: 6.022s
[2025-07-23 19:46:18] y方向算符进度: 6/64, 当前算符: D_(0, 3) * D_(3, 4), 耗时: 4.729s
[2025-07-23 19:46:24] y方向算符进度: 7/64, 当前算符: D_(0, 3) * D_(4, 7), 耗时: 6.026s
[2025-07-23 19:46:31] y方向算符进度: 8/64, 当前算符: D_(0, 3) * D_(5, 6), 耗时: 6.026s
[2025-07-23 19:46:37] y方向算符进度: 9/64, 当前算符: D_(0, 3) * D_(6, 9), 耗时: 6.026s
[2025-07-23 19:46:43] y方向算符进度: 10/64, 当前算符: D_(0, 3) * D_(7, 8), 耗时: 6.027s
[2025-07-23 19:46:49] y方向算符进度: 11/64, 当前算符: D_(0, 3) * D_(8, 11), 耗时: 5.986s
[2025-07-23 19:46:55] y方向算符进度: 12/64, 当前算符: D_(0, 3) * D_(9, 10), 耗时: 6.023s
[2025-07-23 19:47:01] y方向算符进度: 13/64, 当前算符: D_(0, 3) * D_(10, 13), 耗时: 6.023s
[2025-07-23 19:47:07] y方向算符进度: 14/64, 当前算符: D_(0, 3) * D_(11, 12), 耗时: 6.026s
[2025-07-23 19:47:13] y方向算符进度: 15/64, 当前算符: D_(0, 3) * D_(12, 15), 耗时: 5.987s
[2025-07-23 19:47:19] y方向算符进度: 16/64, 当前算符: D_(0, 3) * D_(13, 14), 耗时: 6.025s
[2025-07-23 19:47:25] y方向算符进度: 17/64, 当前算符: D_(0, 3) * D_(16, 19), 耗时: 5.987s
[2025-07-23 19:47:31] y方向算符进度: 18/64, 当前算符: D_(0, 3) * D_(16, 31), 耗时: 6.026s
[2025-07-23 19:47:37] y方向算符进度: 19/64, 当前算符: D_(0, 3) * D_(17, 18), 耗时: 6.024s
[2025-07-23 19:47:43] y方向算符进度: 20/64, 当前算符: D_(0, 3) * D_(17, 30), 耗时: 6.026s
[2025-07-23 19:47:49] y方向算符进度: 21/64, 当前算符: D_(0, 3) * D_(18, 21), 耗时: 5.990s
[2025-07-23 19:47:55] y方向算符进度: 22/64, 当前算符: D_(0, 3) * D_(19, 20), 耗时: 5.987s
[2025-07-23 19:48:01] y方向算符进度: 23/64, 当前算符: D_(0, 3) * D_(20, 23), 耗时: 6.023s
[2025-07-23 19:48:07] y方向算符进度: 24/64, 当前算符: D_(0, 3) * D_(21, 22), 耗时: 6.023s
[2025-07-23 19:48:13] y方向算符进度: 25/64, 当前算符: D_(0, 3) * D_(22, 25), 耗时: 6.026s
[2025-07-23 19:48:19] y方向算符进度: 26/64, 当前算符: D_(0, 3) * D_(23, 24), 耗时: 6.025s
[2025-07-23 19:48:25] y方向算符进度: 27/64, 当前算符: D_(0, 3) * D_(24, 27), 耗时: 6.026s
[2025-07-23 19:48:31] y方向算符进度: 28/64, 当前算符: D_(0, 3) * D_(25, 26), 耗时: 6.023s
[2025-07-23 19:48:37] y方向算符进度: 29/64, 当前算符: D_(0, 3) * D_(26, 29), 耗时: 5.987s
[2025-07-23 19:48:43] y方向算符进度: 30/64, 当前算符: D_(0, 3) * D_(27, 28), 耗时: 6.024s
[2025-07-23 19:48:49] y方向算符进度: 31/64, 当前算符: D_(0, 3) * D_(28, 31), 耗时: 6.023s
[2025-07-23 19:48:55] y方向算符进度: 32/64, 当前算符: D_(0, 3) * D_(29, 30), 耗时: 6.026s
[2025-07-23 19:49:01] y方向算符进度: 33/64, 当前算符: D_(0, 3) * D_(32, 35), 耗时: 6.026s
[2025-07-23 19:49:07] y方向算符进度: 34/64, 当前算符: D_(0, 3) * D_(32, 47), 耗时: 6.023s
[2025-07-23 19:49:13] y方向算符进度: 35/64, 当前算符: D_(0, 3) * D_(33, 34), 耗时: 5.987s
[2025-07-23 19:49:19] y方向算符进度: 36/64, 当前算符: D_(0, 3) * D_(33, 46), 耗时: 6.024s
[2025-07-23 19:49:25] y方向算符进度: 37/64, 当前算符: D_(0, 3) * D_(34, 37), 耗时: 6.026s
[2025-07-23 19:49:31] y方向算符进度: 38/64, 当前算符: D_(0, 3) * D_(35, 36), 耗时: 6.024s
[2025-07-23 19:49:37] y方向算符进度: 39/64, 当前算符: D_(0, 3) * D_(36, 39), 耗时: 6.022s
[2025-07-23 19:49:43] y方向算符进度: 40/64, 当前算符: D_(0, 3) * D_(37, 38), 耗时: 6.028s
[2025-07-23 19:49:49] y方向算符进度: 41/64, 当前算符: D_(0, 3) * D_(38, 41), 耗时: 5.990s
[2025-07-23 19:49:55] y方向算符进度: 42/64, 当前算符: D_(0, 3) * D_(39, 40), 耗时: 6.103s
[2025-07-23 19:50:01] y方向算符进度: 43/64, 当前算符: D_(0, 3) * D_(40, 43), 耗时: 6.024s
[2025-07-23 19:50:07] y方向算符进度: 44/64, 当前算符: D_(0, 3) * D_(41, 42), 耗时: 6.026s
[2025-07-23 19:50:13] y方向算符进度: 45/64, 当前算符: D_(0, 3) * D_(42, 45), 耗时: 5.988s
[2025-07-23 19:50:19] y方向算符进度: 46/64, 当前算符: D_(0, 3) * D_(43, 44), 耗时: 6.022s
[2025-07-23 19:50:25] y方向算符进度: 47/64, 当前算符: D_(0, 3) * D_(44, 47), 耗时: 6.023s
[2025-07-23 19:50:31] y方向算符进度: 48/64, 当前算符: D_(0, 3) * D_(45, 46), 耗时: 6.024s
[2025-07-23 19:50:37] y方向算符进度: 49/64, 当前算符: D_(0, 3) * D_(48, 51), 耗时: 6.026s
[2025-07-23 19:50:43] y方向算符进度: 50/64, 当前算符: D_(0, 3) * D_(48, 63), 耗时: 6.023s
[2025-07-23 19:50:49] y方向算符进度: 51/64, 当前算符: D_(0, 3) * D_(49, 50), 耗时: 5.987s
[2025-07-23 19:50:55] y方向算符进度: 52/64, 当前算符: D_(0, 3) * D_(49, 62), 耗时: 6.023s
[2025-07-23 19:51:01] y方向算符进度: 53/64, 当前算符: D_(0, 3) * D_(50, 53), 耗时: 6.026s
[2025-07-23 19:51:07] y方向算符进度: 54/64, 当前算符: D_(0, 3) * D_(51, 52), 耗时: 6.024s
[2025-07-23 19:51:13] y方向算符进度: 55/64, 当前算符: D_(0, 3) * D_(52, 55), 耗时: 6.027s
[2025-07-23 19:51:19] y方向算符进度: 56/64, 当前算符: D_(0, 3) * D_(53, 54), 耗时: 5.989s
[2025-07-23 19:51:25] y方向算符进度: 57/64, 当前算符: D_(0, 3) * D_(54, 57), 耗时: 5.988s
[2025-07-23 19:51:31] y方向算符进度: 58/64, 当前算符: D_(0, 3) * D_(55, 56), 耗时: 6.023s
[2025-07-23 19:51:37] y方向算符进度: 59/64, 当前算符: D_(0, 3) * D_(56, 59), 耗时: 6.023s
[2025-07-23 19:51:43] y方向算符进度: 60/64, 当前算符: D_(0, 3) * D_(57, 58), 耗时: 6.025s
[2025-07-23 19:51:49] y方向算符进度: 61/64, 当前算符: D_(0, 3) * D_(58, 61), 耗时: 5.988s
[2025-07-23 19:51:56] y方向算符进度: 62/64, 当前算符: D_(0, 3) * D_(59, 60), 耗时: 6.023s
[2025-07-23 19:52:02] y方向算符进度: 63/64, 当前算符: D_(0, 3) * D_(60, 63), 耗时: 6.023s
[2025-07-23 19:52:08] y方向算符进度: 64/64, 当前算符: D_(0, 3) * D_(61, 62), 耗时: 6.025s
[2025-07-23 19:52:08] y方向二聚体相关函数计算完成,耗时: 380.25 秒
[2025-07-23 19:52:08] 计算傅里叶变换...
[2025-07-23 19:52:08] 二聚体结构因子计算完成
[2025-07-23 19:52:09] 二聚体相关函数平均误差: 0.000495
[2025-07-23 19:52:09] 恢复原始样本数: 4096
[2025-07-23 19:52:09] ================================================================================
[2025-07-23 19:52:09] 所有分析完成
