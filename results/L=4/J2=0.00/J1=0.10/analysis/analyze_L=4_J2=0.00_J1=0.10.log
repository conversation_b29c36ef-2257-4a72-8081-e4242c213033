[2025-07-23 21:05:52] ================================================================================
[2025-07-23 21:05:52] 加载量子态: L=4, J2=0.00, J1=0.10
[2025-07-23 21:05:52] 设置样本数为: 1048576
[2025-07-23 21:05:52] 开始生成共享样本集...
[2025-07-23 21:07:03] 样本生成完成,耗时: 70.596 秒
[2025-07-23 21:07:03] ================================================================================
[2025-07-23 21:07:03] 开始计算自旋结构因子...
[2025-07-23 21:07:03] 初始化操作符缓存...
[2025-07-23 21:07:03] 预构建所有自旋相关操作符...
[2025-07-23 21:07:03] 开始计算自旋相关函数...
[2025-07-23 21:07:11] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 8.574s
[2025-07-23 21:07:20] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 9.176s
[2025-07-23 21:07:24] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 3.607s
[2025-07-23 21:07:28] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 3.605s
[2025-07-23 21:07:31] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 3.606s
[2025-07-23 21:07:35] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 3.600s
[2025-07-23 21:07:38] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 3.610s
[2025-07-23 21:07:42] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 3.605s
[2025-07-23 21:07:46] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 3.592s
[2025-07-23 21:07:49] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 3.608s
[2025-07-23 21:07:53] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 3.606s
[2025-07-23 21:07:56] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 3.603s
[2025-07-23 21:08:00] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 3.612s
[2025-07-23 21:08:04] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 3.595s
[2025-07-23 21:08:07] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 3.609s
[2025-07-23 21:08:11] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 3.606s
[2025-07-23 21:08:14] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 3.606s
[2025-07-23 21:08:18] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 3.595s
[2025-07-23 21:08:22] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 3.611s
[2025-07-23 21:08:25] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 3.605s
[2025-07-23 21:08:29] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 3.604s
[2025-07-23 21:08:33] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 3.603s
[2025-07-23 21:08:36] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 3.604s
[2025-07-23 21:08:40] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 3.595s
[2025-07-23 21:08:43] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 3.606s
[2025-07-23 21:08:47] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 3.611s
[2025-07-23 21:08:51] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 3.605s
[2025-07-23 21:08:54] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 3.608s
[2025-07-23 21:08:58] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 3.594s
[2025-07-23 21:09:01] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 3.595s
[2025-07-23 21:09:05] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 3.607s
[2025-07-23 21:09:09] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 3.601s
[2025-07-23 21:09:12] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 3.604s
[2025-07-23 21:09:16] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 3.612s
[2025-07-23 21:09:19] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 3.595s
[2025-07-23 21:09:23] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 3.611s
[2025-07-23 21:09:27] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 3.601s
[2025-07-23 21:09:30] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 3.604s
[2025-07-23 21:09:34] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 3.608s
[2025-07-23 21:09:37] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 3.595s
[2025-07-23 21:09:41] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 3.613s
[2025-07-23 21:09:45] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 3.609s
[2025-07-23 21:09:48] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 3.607s
[2025-07-23 21:09:52] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 3.599s
[2025-07-23 21:09:55] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 3.604s
[2025-07-23 21:09:59] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 3.604s
[2025-07-23 21:10:03] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 3.594s
[2025-07-23 21:10:06] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 3.610s
[2025-07-23 21:10:10] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 3.597s
[2025-07-23 21:10:13] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 3.605s
[2025-07-23 21:10:17] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 3.610s
[2025-07-23 21:10:21] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 3.605s
[2025-07-23 21:10:24] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 3.607s
[2025-07-23 21:10:28] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 3.611s
[2025-07-23 21:10:31] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 3.593s
[2025-07-23 21:10:35] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 3.607s
[2025-07-23 21:10:39] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 3.604s
[2025-07-23 21:10:42] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 3.595s
[2025-07-23 21:10:46] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 3.607s
[2025-07-23 21:10:49] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 3.592s
[2025-07-23 21:10:53] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 3.605s
[2025-07-23 21:10:57] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 3.609s
[2025-07-23 21:11:00] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 3.606s
[2025-07-23 21:11:04] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 3.594s
[2025-07-23 21:11:04] 自旋相关函数计算完成,总耗时 241.26 秒
[2025-07-23 21:11:04] 计算傅里叶变换...
[2025-07-23 21:11:05] 自旋结构因子计算完成
[2025-07-23 21:11:05] 自旋相关函数平均误差: 0.000655
[2025-07-23 21:11:05] ================================================================================
[2025-07-23 21:11:05] 开始计算二聚体结构因子...
[2025-07-23 21:11:05] 识别x和y方向的二聚体...
[2025-07-23 21:11:06] 找到 64 个x方向二聚体和 64 个y方向二聚体
[2025-07-23 21:11:06] 预计算二聚体操作符...
[2025-07-23 21:11:07] 使用x-二聚体 (0, 1) (全局索引 0) 作为参考点, 计算x方向连通二聚体相关函数...
[2025-07-23 21:11:10] x方向算符进度: 1/64, 当前算符: D_(0, 1) * D_(0, 1), 耗时: 3.658s
[2025-07-23 21:11:25] x方向算符进度: 2/64, 当前算符: D_(0, 1) * D_(0, 49), 耗时: 14.751s
[2025-07-23 21:11:30] x方向算符进度: 3/64, 当前算符: D_(0, 1) * D_(1, 16), 耗时: 4.750s
[2025-07-23 21:11:41] x方向算符进度: 4/64, 当前算符: D_(0, 1) * D_(2, 3), 耗时: 11.676s
[2025-07-23 21:11:47] x方向算符进度: 5/64, 当前算符: D_(0, 1) * D_(2, 19), 耗时: 6.023s
[2025-07-23 21:11:53] x方向算符进度: 6/64, 当前算符: D_(0, 1) * D_(3, 50), 耗时: 6.025s
[2025-07-23 21:11:59] x方向算符进度: 7/64, 当前算符: D_(0, 1) * D_(4, 5), 耗时: 6.035s
[2025-07-23 21:12:05] x方向算符进度: 8/64, 当前算符: D_(0, 1) * D_(4, 53), 耗时: 6.010s
[2025-07-23 21:12:11] x方向算符进度: 9/64, 当前算符: D_(0, 1) * D_(5, 20), 耗时: 6.034s
[2025-07-23 21:12:18] x方向算符进度: 10/64, 当前算符: D_(0, 1) * D_(6, 7), 耗时: 6.019s
[2025-07-23 21:12:24] x方向算符进度: 11/64, 当前算符: D_(0, 1) * D_(6, 23), 耗时: 6.035s
[2025-07-23 21:12:30] x方向算符进度: 12/64, 当前算符: D_(0, 1) * D_(7, 54), 耗时: 6.024s
[2025-07-23 21:12:36] x方向算符进度: 13/64, 当前算符: D_(0, 1) * D_(8, 9), 耗时: 6.042s
[2025-07-23 21:12:42] x方向算符进度: 14/64, 当前算符: D_(0, 1) * D_(8, 57), 耗时: 6.009s
[2025-07-23 21:12:48] x方向算符进度: 15/64, 当前算符: D_(0, 1) * D_(9, 24), 耗时: 6.025s
[2025-07-23 21:12:54] x方向算符进度: 16/64, 当前算符: D_(0, 1) * D_(10, 11), 耗时: 6.027s
[2025-07-23 21:13:00] x方向算符进度: 17/64, 当前算符: D_(0, 1) * D_(10, 27), 耗时: 6.016s
[2025-07-23 21:13:06] x方向算符进度: 18/64, 当前算符: D_(0, 1) * D_(11, 58), 耗时: 6.007s
[2025-07-23 21:13:12] x方向算符进度: 19/64, 当前算符: D_(0, 1) * D_(12, 13), 耗时: 6.030s
[2025-07-23 21:13:18] x方向算符进度: 20/64, 当前算符: D_(0, 1) * D_(12, 61), 耗时: 6.009s
[2025-07-23 21:13:24] x方向算符进度: 21/64, 当前算符: D_(0, 1) * D_(13, 28), 耗时: 6.025s
[2025-07-23 21:13:30] x方向算符进度: 22/64, 当前算符: D_(0, 1) * D_(14, 15), 耗时: 6.024s
[2025-07-23 21:13:36] x方向算符进度: 23/64, 当前算符: D_(0, 1) * D_(14, 31), 耗时: 6.038s
[2025-07-23 21:13:42] x方向算符进度: 24/64, 当前算符: D_(0, 1) * D_(15, 62), 耗时: 6.008s
[2025-07-23 21:13:48] x方向算符进度: 25/64, 当前算符: D_(0, 1) * D_(16, 17), 耗时: 6.034s
[2025-07-23 21:13:54] x方向算符进度: 26/64, 当前算符: D_(0, 1) * D_(17, 32), 耗时: 6.019s
[2025-07-23 21:14:00] x方向算符进度: 27/64, 当前算符: D_(0, 1) * D_(18, 19), 耗时: 6.033s
[2025-07-23 21:14:06] x方向算符进度: 28/64, 当前算符: D_(0, 1) * D_(18, 35), 耗时: 6.014s
[2025-07-23 21:14:12] x方向算符进度: 29/64, 当前算符: D_(0, 1) * D_(20, 21), 耗时: 6.030s
[2025-07-23 21:14:18] x方向算符进度: 30/64, 当前算符: D_(0, 1) * D_(21, 36), 耗时: 6.008s
[2025-07-23 21:14:24] x方向算符进度: 31/64, 当前算符: D_(0, 1) * D_(22, 23), 耗时: 6.023s
[2025-07-23 21:14:30] x方向算符进度: 32/64, 当前算符: D_(0, 1) * D_(22, 39), 耗时: 6.043s
[2025-07-23 21:14:36] x方向算符进度: 33/64, 当前算符: D_(0, 1) * D_(24, 25), 耗时: 6.019s
[2025-07-23 21:14:42] x方向算符进度: 34/64, 当前算符: D_(0, 1) * D_(25, 40), 耗时: 6.036s
[2025-07-23 21:14:48] x方向算符进度: 35/64, 当前算符: D_(0, 1) * D_(26, 27), 耗时: 6.026s
[2025-07-23 21:14:54] x方向算符进度: 36/64, 当前算符: D_(0, 1) * D_(26, 43), 耗时: 6.008s
[2025-07-23 21:15:00] x方向算符进度: 37/64, 当前算符: D_(0, 1) * D_(28, 29), 耗时: 6.040s
[2025-07-23 21:15:06] x方向算符进度: 38/64, 当前算符: D_(0, 1) * D_(29, 44), 耗时: 6.024s
[2025-07-23 21:15:12] x方向算符进度: 39/64, 当前算符: D_(0, 1) * D_(30, 31), 耗时: 6.038s
[2025-07-23 21:15:18] x方向算符进度: 40/64, 当前算符: D_(0, 1) * D_(30, 47), 耗时: 6.186s
[2025-07-23 21:15:24] x方向算符进度: 41/64, 当前算符: D_(0, 1) * D_(32, 33), 耗时: 6.036s
[2025-07-23 21:15:30] x方向算符进度: 42/64, 当前算符: D_(0, 1) * D_(33, 48), 耗时: 6.021s
[2025-07-23 21:15:37] x方向算符进度: 43/64, 当前算符: D_(0, 1) * D_(34, 35), 耗时: 6.025s
[2025-07-23 21:15:43] x方向算符进度: 44/64, 当前算符: D_(0, 1) * D_(34, 51), 耗时: 6.021s
[2025-07-23 21:15:49] x方向算符进度: 45/64, 当前算符: D_(0, 1) * D_(36, 37), 耗时: 6.031s
[2025-07-23 21:15:55] x方向算符进度: 46/64, 当前算符: D_(0, 1) * D_(37, 52), 耗时: 6.009s
[2025-07-23 21:16:01] x方向算符进度: 47/64, 当前算符: D_(0, 1) * D_(38, 39), 耗时: 6.016s
[2025-07-23 21:16:07] x方向算符进度: 48/64, 当前算符: D_(0, 1) * D_(38, 55), 耗时: 6.029s
[2025-07-23 21:16:13] x方向算符进度: 49/64, 当前算符: D_(0, 1) * D_(40, 41), 耗时: 6.009s
[2025-07-23 21:16:19] x方向算符进度: 50/64, 当前算符: D_(0, 1) * D_(41, 56), 耗时: 6.037s
[2025-07-23 21:16:25] x方向算符进度: 51/64, 当前算符: D_(0, 1) * D_(42, 43), 耗时: 6.012s
[2025-07-23 21:16:31] x方向算符进度: 52/64, 当前算符: D_(0, 1) * D_(42, 59), 耗时: 6.024s
[2025-07-23 21:16:37] x方向算符进度: 53/64, 当前算符: D_(0, 1) * D_(44, 45), 耗时: 6.041s
[2025-07-23 21:16:43] x方向算符进度: 54/64, 当前算符: D_(0, 1) * D_(45, 60), 耗时: 6.013s
[2025-07-23 21:16:49] x方向算符进度: 55/64, 当前算符: D_(0, 1) * D_(46, 47), 耗时: 6.045s
[2025-07-23 21:16:55] x方向算符进度: 56/64, 当前算符: D_(0, 1) * D_(46, 63), 耗时: 6.024s
[2025-07-23 21:17:01] x方向算符进度: 57/64, 当前算符: D_(0, 1) * D_(48, 49), 耗时: 6.041s
[2025-07-23 21:17:07] x方向算符进度: 58/64, 当前算符: D_(0, 1) * D_(50, 51), 耗时: 6.011s
[2025-07-23 21:17:13] x方向算符进度: 59/64, 当前算符: D_(0, 1) * D_(52, 53), 耗时: 6.040s
[2025-07-23 21:17:19] x方向算符进度: 60/64, 当前算符: D_(0, 1) * D_(54, 55), 耗时: 6.008s
[2025-07-23 21:17:25] x方向算符进度: 61/64, 当前算符: D_(0, 1) * D_(56, 57), 耗时: 6.036s
[2025-07-23 21:17:31] x方向算符进度: 62/64, 当前算符: D_(0, 1) * D_(58, 59), 耗时: 6.018s
[2025-07-23 21:17:37] x方向算符进度: 63/64, 当前算符: D_(0, 1) * D_(60, 61), 耗时: 6.007s
[2025-07-23 21:17:43] x方向算符进度: 64/64, 当前算符: D_(0, 1) * D_(62, 63), 耗时: 6.035s
[2025-07-23 21:17:43] x方向二聚体相关函数计算完成,耗时: 396.55 秒
[2025-07-23 21:17:43] --------------------------------------------------------------------------------
[2025-07-23 21:17:43] 使用y-二聚体 (0, 3) (全局索引 64) 作为参考点, 计算y方向连通二聚体相关函数...
[2025-07-23 21:17:47] y方向算符进度: 1/64, 当前算符: D_(0, 3) * D_(0, 3), 耗时: 3.603s
[2025-07-23 21:17:51] y方向算符进度: 2/64, 当前算符: D_(0, 3) * D_(0, 15), 耗时: 4.736s
[2025-07-23 21:17:57] y方向算符进度: 3/64, 当前算符: D_(0, 3) * D_(1, 2), 耗时: 6.029s
[2025-07-23 21:18:03] y方向算符进度: 4/64, 当前算符: D_(0, 3) * D_(1, 14), 耗时: 6.044s
[2025-07-23 21:18:10] y方向算符进度: 5/64, 当前算符: D_(0, 3) * D_(2, 5), 耗时: 6.043s
[2025-07-23 21:18:14] y方向算符进度: 6/64, 当前算符: D_(0, 3) * D_(3, 4), 耗时: 4.734s
[2025-07-23 21:18:20] y方向算符进度: 7/64, 当前算符: D_(0, 3) * D_(4, 7), 耗时: 6.033s
[2025-07-23 21:18:26] y方向算符进度: 8/64, 当前算符: D_(0, 3) * D_(5, 6), 耗时: 6.031s
[2025-07-23 21:18:32] y方向算符进度: 9/64, 当前算符: D_(0, 3) * D_(6, 9), 耗时: 6.032s
[2025-07-23 21:18:38] y方向算符进度: 10/64, 当前算符: D_(0, 3) * D_(7, 8), 耗时: 6.044s
[2025-07-23 21:18:44] y方向算符进度: 11/64, 当前算符: D_(0, 3) * D_(8, 11), 耗时: 6.008s
[2025-07-23 21:18:50] y方向算符进度: 12/64, 当前算符: D_(0, 3) * D_(9, 10), 耗时: 6.039s
[2025-07-23 21:18:56] y方向算符进度: 13/64, 当前算符: D_(0, 3) * D_(10, 13), 耗时: 6.029s
[2025-07-23 21:19:03] y方向算符进度: 14/64, 当前算符: D_(0, 3) * D_(11, 12), 耗时: 6.031s
[2025-07-23 21:19:09] y方向算符进度: 15/64, 当前算符: D_(0, 3) * D_(12, 15), 耗时: 6.011s
[2025-07-23 21:19:15] y方向算符进度: 16/64, 当前算符: D_(0, 3) * D_(13, 14), 耗时: 6.034s
[2025-07-23 21:19:21] y方向算符进度: 17/64, 当前算符: D_(0, 3) * D_(16, 19), 耗时: 6.014s
[2025-07-23 21:19:27] y方向算符进度: 18/64, 当前算符: D_(0, 3) * D_(16, 31), 耗时: 6.038s
[2025-07-23 21:19:33] y方向算符进度: 19/64, 当前算符: D_(0, 3) * D_(17, 18), 耗时: 6.031s
[2025-07-23 21:19:39] y方向算符进度: 20/64, 当前算符: D_(0, 3) * D_(17, 30), 耗时: 6.048s
[2025-07-23 21:19:45] y方向算符进度: 21/64, 当前算符: D_(0, 3) * D_(18, 21), 耗时: 6.009s
[2025-07-23 21:19:51] y方向算符进度: 22/64, 当前算符: D_(0, 3) * D_(19, 20), 耗时: 6.014s
[2025-07-23 21:19:57] y方向算符进度: 23/64, 当前算符: D_(0, 3) * D_(20, 23), 耗时: 6.023s
[2025-07-23 21:20:03] y方向算符进度: 24/64, 当前算符: D_(0, 3) * D_(21, 22), 耗时: 6.037s
[2025-07-23 21:20:09] y方向算符进度: 25/64, 当前算符: D_(0, 3) * D_(22, 25), 耗时: 6.032s
[2025-07-23 21:20:15] y方向算符进度: 26/64, 当前算符: D_(0, 3) * D_(23, 24), 耗时: 6.031s
[2025-07-23 21:20:21] y方向算符进度: 27/64, 当前算符: D_(0, 3) * D_(24, 27), 耗时: 6.031s
[2025-07-23 21:20:27] y方向算符进度: 28/64, 当前算符: D_(0, 3) * D_(25, 26), 耗时: 6.028s
[2025-07-23 21:20:33] y方向算符进度: 29/64, 当前算符: D_(0, 3) * D_(26, 29), 耗时: 6.010s
[2025-07-23 21:20:39] y方向算符进度: 30/64, 当前算符: D_(0, 3) * D_(27, 28), 耗时: 6.044s
[2025-07-23 21:20:45] y方向算符进度: 31/64, 当前算符: D_(0, 3) * D_(28, 31), 耗时: 6.028s
[2025-07-23 21:20:51] y方向算符进度: 32/64, 当前算符: D_(0, 3) * D_(29, 30), 耗时: 6.039s
[2025-07-23 21:20:57] y方向算符进度: 33/64, 当前算符: D_(0, 3) * D_(32, 35), 耗时: 6.031s
[2025-07-23 21:21:03] y方向算符进度: 34/64, 当前算符: D_(0, 3) * D_(32, 47), 耗时: 6.042s
[2025-07-23 21:21:09] y方向算符进度: 35/64, 当前算符: D_(0, 3) * D_(33, 34), 耗时: 6.015s
[2025-07-23 21:21:15] y方向算符进度: 36/64, 当前算符: D_(0, 3) * D_(33, 46), 耗时: 6.037s
[2025-07-23 21:21:21] y方向算符进度: 37/64, 当前算符: D_(0, 3) * D_(34, 37), 耗时: 6.031s
[2025-07-23 21:21:27] y方向算符进度: 38/64, 当前算符: D_(0, 3) * D_(35, 36), 耗时: 6.030s
[2025-07-23 21:21:33] y方向算符进度: 39/64, 当前算符: D_(0, 3) * D_(36, 39), 耗时: 6.037s
[2025-07-23 21:21:39] y方向算符进度: 40/64, 当前算符: D_(0, 3) * D_(37, 38), 耗时: 6.045s
[2025-07-23 21:21:45] y方向算符进度: 41/64, 当前算符: D_(0, 3) * D_(38, 41), 耗时: 6.009s
[2025-07-23 21:21:51] y方向算符进度: 42/64, 当前算符: D_(0, 3) * D_(39, 40), 耗时: 6.037s
[2025-07-23 21:21:57] y方向算符进度: 43/64, 当前算符: D_(0, 3) * D_(40, 43), 耗时: 6.041s
[2025-07-23 21:22:03] y方向算符进度: 44/64, 当前算符: D_(0, 3) * D_(41, 42), 耗时: 6.032s
[2025-07-23 21:22:09] y方向算符进度: 45/64, 当前算符: D_(0, 3) * D_(42, 45), 耗时: 6.011s
[2025-07-23 21:22:15] y方向算符进度: 46/64, 当前算符: D_(0, 3) * D_(43, 44), 耗时: 6.042s
[2025-07-23 21:22:22] y方向算符进度: 47/64, 当前算符: D_(0, 3) * D_(44, 47), 耗时: 6.022s
[2025-07-23 21:22:28] y方向算符进度: 48/64, 当前算符: D_(0, 3) * D_(45, 46), 耗时: 6.043s
[2025-07-23 21:22:34] y方向算符进度: 49/64, 当前算符: D_(0, 3) * D_(48, 51), 耗时: 6.032s
[2025-07-23 21:22:40] y方向算符进度: 50/64, 当前算符: D_(0, 3) * D_(48, 63), 耗时: 6.044s
[2025-07-23 21:22:46] y方向算符进度: 51/64, 当前算符: D_(0, 3) * D_(49, 50), 耗时: 6.010s
[2025-07-23 21:22:52] y方向算符进度: 52/64, 当前算符: D_(0, 3) * D_(49, 62), 耗时: 6.042s
[2025-07-23 21:22:58] y方向算符进度: 53/64, 当前算符: D_(0, 3) * D_(50, 53), 耗时: 6.032s
[2025-07-23 21:23:04] y方向算符进度: 54/64, 当前算符: D_(0, 3) * D_(51, 52), 耗时: 6.023s
[2025-07-23 21:23:10] y方向算符进度: 55/64, 当前算符: D_(0, 3) * D_(52, 55), 耗时: 6.043s
[2025-07-23 21:23:16] y方向算符进度: 56/64, 当前算符: D_(0, 3) * D_(53, 54), 耗时: 6.011s
[2025-07-23 21:23:22] y方向算符进度: 57/64, 当前算符: D_(0, 3) * D_(54, 57), 耗时: 6.011s
[2025-07-23 21:23:28] y方向算符进度: 58/64, 当前算符: D_(0, 3) * D_(55, 56), 耗时: 6.047s
[2025-07-23 21:23:34] y方向算符进度: 59/64, 当前算符: D_(0, 3) * D_(56, 59), 耗时: 6.025s
[2025-07-23 21:23:40] y方向算符进度: 60/64, 当前算符: D_(0, 3) * D_(57, 58), 耗时: 6.033s
[2025-07-23 21:23:46] y方向算符进度: 61/64, 当前算符: D_(0, 3) * D_(58, 61), 耗时: 6.008s
[2025-07-23 21:23:52] y方向算符进度: 62/64, 当前算符: D_(0, 3) * D_(59, 60), 耗时: 6.046s
[2025-07-23 21:23:58] y方向算符进度: 63/64, 当前算符: D_(0, 3) * D_(60, 63), 耗时: 6.028s
[2025-07-23 21:24:04] y方向算符进度: 64/64, 当前算符: D_(0, 3) * D_(61, 62), 耗时: 6.038s
[2025-07-23 21:24:04] y方向二聚体相关函数计算完成,耗时: 381.00 秒
[2025-07-23 21:24:04] 计算傅里叶变换...
[2025-07-23 21:24:05] 二聚体结构因子计算完成
[2025-07-23 21:24:06] 二聚体相关函数平均误差: 0.000492
[2025-07-23 21:24:06] 恢复原始样本数: 4096
[2025-07-23 21:24:06] ================================================================================
[2025-07-23 21:24:06] 所有分析完成
