[2025-07-23 19:15:37] ================================================================================
[2025-07-23 19:15:37] 加载量子态: L=4, J2=0.00, J1=0.04
[2025-07-23 19:15:37] 设置样本数为: 1048576
[2025-07-23 19:15:37] 开始生成共享样本集...
[2025-07-23 19:16:48] 样本生成完成,耗时: 70.251 秒
[2025-07-23 19:16:48] ================================================================================
[2025-07-23 19:16:48] 开始计算自旋结构因子...
[2025-07-23 19:16:48] 初始化操作符缓存...
[2025-07-23 19:16:48] 预构建所有自旋相关操作符...
[2025-07-23 19:16:48] 开始计算自旋相关函数...
[2025-07-23 19:16:56] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 8.568s
[2025-07-23 19:17:06] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 9.190s
[2025-07-23 19:17:09] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 3.598s
[2025-07-23 19:17:13] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 3.598s
[2025-07-23 19:17:16] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 3.598s
[2025-07-23 19:17:20] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 3.598s
[2025-07-23 19:17:24] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 3.598s
[2025-07-23 19:17:27] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 3.576s
[2025-07-23 19:17:31] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 3.578s
[2025-07-23 19:17:34] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 3.598s
[2025-07-23 19:17:38] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 3.597s
[2025-07-23 19:17:42] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 3.598s
[2025-07-23 19:17:45] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 3.580s
[2025-07-23 19:17:49] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 3.576s
[2025-07-23 19:17:52] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 3.578s
[2025-07-23 19:17:56] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 3.597s
[2025-07-23 19:17:59] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 3.598s
[2025-07-23 19:18:03] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 3.579s
[2025-07-23 19:18:07] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 3.578s
[2025-07-23 19:18:10] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 3.580s
[2025-07-23 19:18:14] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 3.599s
[2025-07-23 19:18:17] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 3.598s
[2025-07-23 19:18:21] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 3.598s
[2025-07-23 19:18:25] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 3.578s
[2025-07-23 19:18:28] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 3.578s
[2025-07-23 19:18:32] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 3.598s
[2025-07-23 19:18:35] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 3.598s
[2025-07-23 19:18:39] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 3.599s
[2025-07-23 19:18:43] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 3.578s
[2025-07-23 19:18:46] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 3.578s
[2025-07-23 19:18:50] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 3.578s
[2025-07-23 19:18:53] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 3.598s
[2025-07-23 19:18:57] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 3.598s
[2025-07-23 19:19:00] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 3.576s
[2025-07-23 19:19:04] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 3.578s
[2025-07-23 19:19:08] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 3.578s
[2025-07-23 19:19:11] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 3.598s
[2025-07-23 19:19:15] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 3.598s
[2025-07-23 19:19:18] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 3.598s
[2025-07-23 19:19:22] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 3.577s
[2025-07-23 19:19:26] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 3.577s
[2025-07-23 19:19:29] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 3.598s
[2025-07-23 19:19:33] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 3.597s
[2025-07-23 19:19:36] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 3.598s
[2025-07-23 19:19:40] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 3.577s
[2025-07-23 19:19:44] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 3.577s
[2025-07-23 19:19:47] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 3.583s
[2025-07-23 19:19:51] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 3.578s
[2025-07-23 19:19:54] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 3.578s
[2025-07-23 19:19:58] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 3.597s
[2025-07-23 19:20:01] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 3.596s
[2025-07-23 19:20:05] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 3.598s
[2025-07-23 19:20:09] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 3.583s
[2025-07-23 19:20:12] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 3.584s
[2025-07-23 19:20:16] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 3.577s
[2025-07-23 19:20:19] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 3.598s
[2025-07-23 19:20:23] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 3.598s
[2025-07-23 19:20:27] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 3.580s
[2025-07-23 19:20:30] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 3.578s
[2025-07-23 19:20:34] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 3.578s
[2025-07-23 19:20:37] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 3.597s
[2025-07-23 19:20:41] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 3.598s
[2025-07-23 19:20:45] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 3.596s
[2025-07-23 19:20:48] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 3.576s
[2025-07-23 19:20:48] 自旋相关函数计算完成,总耗时 240.31 秒
[2025-07-23 19:20:48] 计算傅里叶变换...
[2025-07-23 19:20:49] 自旋结构因子计算完成
[2025-07-23 19:20:50] 自旋相关函数平均误差: 0.000650
[2025-07-23 19:20:50] ================================================================================
[2025-07-23 19:20:50] 开始计算二聚体结构因子...
[2025-07-23 19:20:50] 识别x和y方向的二聚体...
[2025-07-23 19:20:50] 找到 64 个x方向二聚体和 64 个y方向二聚体
[2025-07-23 19:20:50] 预计算二聚体操作符...
[2025-07-23 19:20:51] 使用x-二聚体 (0, 1) (全局索引 0) 作为参考点, 计算x方向连通二聚体相关函数...
[2025-07-23 19:20:54] x方向算符进度: 1/64, 当前算符: D_(0, 1) * D_(0, 1), 耗时: 3.614s
[2025-07-23 19:21:09] x方向算符进度: 2/64, 当前算符: D_(0, 1) * D_(0, 49), 耗时: 14.727s
[2025-07-23 19:21:14] x方向算符进度: 3/64, 当前算符: D_(0, 1) * D_(1, 16), 耗时: 4.737s
[2025-07-23 19:21:25] x方向算符进度: 4/64, 当前算符: D_(0, 1) * D_(2, 3), 耗时: 11.580s
[2025-07-23 19:21:31] x方向算符进度: 5/64, 当前算符: D_(0, 1) * D_(2, 19), 耗时: 6.008s
[2025-07-23 19:21:37] x方向算符进度: 6/64, 当前算符: D_(0, 1) * D_(3, 50), 耗时: 6.008s
[2025-07-23 19:21:43] x方向算符进度: 7/64, 当前算符: D_(0, 1) * D_(4, 5), 耗时: 5.965s
[2025-07-23 19:21:49] x方向算符进度: 8/64, 当前算符: D_(0, 1) * D_(4, 53), 耗时: 5.965s
[2025-07-23 19:21:55] x方向算符进度: 9/64, 当前算符: D_(0, 1) * D_(5, 20), 耗时: 6.007s
[2025-07-23 19:22:01] x方向算符进度: 10/64, 当前算符: D_(0, 1) * D_(6, 7), 耗时: 6.006s
[2025-07-23 19:22:07] x方向算符进度: 11/64, 当前算符: D_(0, 1) * D_(6, 23), 耗时: 6.007s
[2025-07-23 19:22:13] x方向算符进度: 12/64, 当前算符: D_(0, 1) * D_(7, 54), 耗时: 6.007s
[2025-07-23 19:22:19] x方向算符进度: 13/64, 当前算符: D_(0, 1) * D_(8, 9), 耗时: 5.964s
[2025-07-23 19:22:25] x方向算符进度: 14/64, 当前算符: D_(0, 1) * D_(8, 57), 耗时: 5.965s
[2025-07-23 19:22:31] x方向算符进度: 15/64, 当前算符: D_(0, 1) * D_(9, 24), 耗时: 6.007s
[2025-07-23 19:22:37] x方向算符进度: 16/64, 当前算符: D_(0, 1) * D_(10, 11), 耗时: 6.006s
[2025-07-23 19:22:43] x方向算符进度: 17/64, 当前算符: D_(0, 1) * D_(10, 27), 耗时: 6.004s
[2025-07-23 19:22:49] x方向算符进度: 18/64, 当前算符: D_(0, 1) * D_(11, 58), 耗时: 5.966s
[2025-07-23 19:22:55] x方向算符进度: 19/64, 当前算符: D_(0, 1) * D_(12, 13), 耗时: 5.962s
[2025-07-23 19:23:01] x方向算符进度: 20/64, 当前算符: D_(0, 1) * D_(12, 61), 耗时: 5.969s
[2025-07-23 19:23:07] x方向算符进度: 21/64, 当前算符: D_(0, 1) * D_(13, 28), 耗时: 6.006s
[2025-07-23 19:23:13] x方向算符进度: 22/64, 当前算符: D_(0, 1) * D_(14, 15), 耗时: 6.006s
[2025-07-23 19:23:19] x方向算符进度: 23/64, 当前算符: D_(0, 1) * D_(14, 31), 耗时: 5.965s
[2025-07-23 19:23:25] x方向算符进度: 24/64, 当前算符: D_(0, 1) * D_(15, 62), 耗时: 5.965s
[2025-07-23 19:23:31] x方向算符进度: 25/64, 当前算符: D_(0, 1) * D_(16, 17), 耗时: 5.965s
[2025-07-23 19:23:37] x方向算符进度: 26/64, 当前算符: D_(0, 1) * D_(17, 32), 耗时: 6.004s
[2025-07-23 19:23:43] x方向算符进度: 27/64, 当前算符: D_(0, 1) * D_(18, 19), 耗时: 6.006s
[2025-07-23 19:23:49] x方向算符进度: 28/64, 当前算符: D_(0, 1) * D_(18, 35), 耗时: 6.004s
[2025-07-23 19:23:55] x方向算符进度: 29/64, 当前算符: D_(0, 1) * D_(20, 21), 耗时: 6.013s
[2025-07-23 19:24:01] x方向算符进度: 30/64, 当前算符: D_(0, 1) * D_(21, 36), 耗时: 5.961s
[2025-07-23 19:24:07] x方向算符进度: 31/64, 当前算符: D_(0, 1) * D_(22, 23), 耗时: 6.006s
[2025-07-23 19:24:13] x方向算符进度: 32/64, 当前算符: D_(0, 1) * D_(22, 39), 耗时: 6.005s
[2025-07-23 19:24:19] x方向算符进度: 33/64, 当前算符: D_(0, 1) * D_(24, 25), 耗时: 6.003s
[2025-07-23 19:24:25] x方向算符进度: 34/64, 当前算符: D_(0, 1) * D_(25, 40), 耗时: 6.015s
[2025-07-23 19:24:31] x方向算符进度: 35/64, 当前算符: D_(0, 1) * D_(26, 27), 耗时: 5.962s
[2025-07-23 19:24:37] x方向算符进度: 36/64, 当前算符: D_(0, 1) * D_(26, 43), 耗时: 5.963s
[2025-07-23 19:24:43] x方向算符进度: 37/64, 当前算符: D_(0, 1) * D_(28, 29), 耗时: 6.005s
[2025-07-23 19:24:49] x方向算符进度: 38/64, 当前算符: D_(0, 1) * D_(29, 44), 耗时: 6.005s
[2025-07-23 19:24:55] x方向算符进度: 39/64, 当前算符: D_(0, 1) * D_(30, 31), 耗时: 5.963s
[2025-07-23 19:25:01] x方向算符进度: 40/64, 当前算符: D_(0, 1) * D_(30, 47), 耗时: 6.063s
[2025-07-23 19:25:07] x方向算符进度: 41/64, 当前算符: D_(0, 1) * D_(32, 33), 耗时: 5.964s
[2025-07-23 19:25:13] x方向算符进度: 42/64, 当前算符: D_(0, 1) * D_(33, 48), 耗时: 6.004s
[2025-07-23 19:25:19] x方向算符进度: 43/64, 当前算符: D_(0, 1) * D_(34, 35), 耗时: 6.003s
[2025-07-23 19:25:25] x方向算符进度: 44/64, 当前算符: D_(0, 1) * D_(34, 51), 耗时: 6.066s
[2025-07-23 19:25:31] x方向算符进度: 45/64, 当前算符: D_(0, 1) * D_(36, 37), 耗时: 5.960s
[2025-07-23 19:25:37] x方向算符进度: 46/64, 当前算符: D_(0, 1) * D_(37, 52), 耗时: 5.960s
[2025-07-23 19:25:43] x方向算符进度: 47/64, 当前算符: D_(0, 1) * D_(38, 39), 耗时: 5.964s
[2025-07-23 19:25:49] x方向算符进度: 48/64, 当前算符: D_(0, 1) * D_(38, 55), 耗时: 5.961s
[2025-07-23 19:25:55] x方向算符进度: 49/64, 当前算符: D_(0, 1) * D_(40, 41), 耗时: 5.961s
[2025-07-23 19:26:01] x方向算符进度: 50/64, 当前算符: D_(0, 1) * D_(41, 56), 耗时: 6.002s
[2025-07-23 19:26:07] x方向算符进度: 51/64, 当前算符: D_(0, 1) * D_(42, 43), 耗时: 6.002s
[2025-07-23 19:26:13] x方向算符进度: 52/64, 当前算符: D_(0, 1) * D_(42, 59), 耗时: 6.005s
[2025-07-23 19:26:19] x方向算符进度: 53/64, 当前算符: D_(0, 1) * D_(44, 45), 耗时: 5.963s
[2025-07-23 19:26:25] x方向算符进度: 54/64, 当前算符: D_(0, 1) * D_(45, 60), 耗时: 5.962s
[2025-07-23 19:26:31] x方向算符进度: 55/64, 当前算符: D_(0, 1) * D_(46, 47), 耗时: 6.004s
[2025-07-23 19:26:37] x方向算符进度: 56/64, 当前算符: D_(0, 1) * D_(46, 63), 耗时: 6.005s
[2025-07-23 19:26:43] x方向算符进度: 57/64, 当前算符: D_(0, 1) * D_(48, 49), 耗时: 6.005s
[2025-07-23 19:26:49] x方向算符进度: 58/64, 当前算符: D_(0, 1) * D_(50, 51), 耗时: 5.962s
[2025-07-23 19:26:55] x方向算符进度: 59/64, 当前算符: D_(0, 1) * D_(52, 53), 耗时: 5.964s
[2025-07-23 19:27:01] x方向算符进度: 60/64, 当前算符: D_(0, 1) * D_(54, 55), 耗时: 5.961s
[2025-07-23 19:27:07] x方向算符进度: 61/64, 当前算符: D_(0, 1) * D_(56, 57), 耗时: 6.005s
[2025-07-23 19:27:13] x方向算符进度: 62/64, 当前算符: D_(0, 1) * D_(58, 59), 耗时: 6.003s
[2025-07-23 19:27:19] x方向算符进度: 63/64, 当前算符: D_(0, 1) * D_(60, 61), 耗时: 5.964s
[2025-07-23 19:27:25] x方向算符进度: 64/64, 当前算符: D_(0, 1) * D_(62, 63), 耗时: 5.964s
[2025-07-23 19:27:25] x方向二聚体相关函数计算完成,耗时: 394.05 秒
[2025-07-23 19:27:25] --------------------------------------------------------------------------------
[2025-07-23 19:27:25] 使用y-二聚体 (0, 3) (全局索引 64) 作为参考点, 计算y方向连通二聚体相关函数...
[2025-07-23 19:27:28] y方向算符进度: 1/64, 当前算符: D_(0, 3) * D_(0, 3), 耗时: 3.596s
[2025-07-23 19:27:33] y方向算符进度: 2/64, 当前算符: D_(0, 3) * D_(0, 15), 耗时: 4.714s
[2025-07-23 19:27:39] y方向算符进度: 3/64, 当前算符: D_(0, 3) * D_(1, 2), 耗时: 6.004s
[2025-07-23 19:27:45] y方向算符进度: 4/64, 当前算符: D_(0, 3) * D_(1, 14), 耗时: 6.015s
[2025-07-23 19:27:51] y方向算符进度: 5/64, 当前算符: D_(0, 3) * D_(2, 5), 耗时: 5.967s
[2025-07-23 19:27:56] y方向算符进度: 6/64, 当前算符: D_(0, 3) * D_(3, 4), 耗时: 4.693s
[2025-07-23 19:28:02] y方向算符进度: 7/64, 当前算符: D_(0, 3) * D_(4, 7), 耗时: 6.014s
[2025-07-23 19:28:08] y方向算符进度: 8/64, 当前算符: D_(0, 3) * D_(5, 6), 耗时: 6.014s
[2025-07-23 19:28:14] y方向算符进度: 9/64, 当前算符: D_(0, 3) * D_(6, 9), 耗时: 6.013s
[2025-07-23 19:28:20] y方向算符进度: 10/64, 当前算符: D_(0, 3) * D_(7, 8), 耗时: 5.977s
[2025-07-23 19:28:26] y方向算符进度: 11/64, 当前算符: D_(0, 3) * D_(8, 11), 耗时: 5.978s
[2025-07-23 19:28:32] y方向算符进度: 12/64, 当前算符: D_(0, 3) * D_(9, 10), 耗时: 5.967s
[2025-07-23 19:28:38] y方向算符进度: 13/64, 当前算符: D_(0, 3) * D_(10, 13), 耗时: 6.005s
[2025-07-23 19:28:44] y方向算符进度: 14/64, 当前算符: D_(0, 3) * D_(11, 12), 耗时: 6.012s
[2025-07-23 19:28:50] y方向算符进度: 15/64, 当前算符: D_(0, 3) * D_(12, 15), 耗时: 5.970s
[2025-07-23 19:28:56] y方向算符进度: 16/64, 当前算符: D_(0, 3) * D_(13, 14), 耗时: 5.967s
[2025-07-23 19:29:02] y方向算符进度: 17/64, 当前算符: D_(0, 3) * D_(16, 19), 耗时: 5.967s
[2025-07-23 19:29:08] y方向算符进度: 18/64, 当前算符: D_(0, 3) * D_(16, 31), 耗时: 6.014s
[2025-07-23 19:29:14] y方向算符进度: 19/64, 当前算符: D_(0, 3) * D_(17, 18), 耗时: 6.006s
[2025-07-23 19:29:20] y方向算符进度: 20/64, 当前算符: D_(0, 3) * D_(17, 30), 耗时: 6.014s
[2025-07-23 19:29:26] y方向算符进度: 21/64, 当前算符: D_(0, 3) * D_(18, 21), 耗时: 5.967s
[2025-07-23 19:29:32] y方向算符进度: 22/64, 当前算符: D_(0, 3) * D_(19, 20), 耗时: 5.974s
[2025-07-23 19:29:38] y方向算符进度: 23/64, 当前算符: D_(0, 3) * D_(20, 23), 耗时: 6.005s
[2025-07-23 19:29:44] y方向算符进度: 24/64, 当前算符: D_(0, 3) * D_(21, 22), 耗时: 6.004s
[2025-07-23 19:29:50] y方向算符进度: 25/64, 当前算符: D_(0, 3) * D_(22, 25), 耗时: 6.012s
[2025-07-23 19:29:56] y方向算符进度: 26/64, 当前算符: D_(0, 3) * D_(23, 24), 耗时: 6.009s
[2025-07-23 19:30:02] y方向算符进度: 27/64, 当前算符: D_(0, 3) * D_(24, 27), 耗时: 6.010s
[2025-07-23 19:30:08] y方向算符进度: 28/64, 当前算符: D_(0, 3) * D_(25, 26), 耗时: 6.004s
[2025-07-23 19:30:14] y方向算符进度: 29/64, 当前算符: D_(0, 3) * D_(26, 29), 耗时: 5.967s
[2025-07-23 19:30:20] y方向算符进度: 30/64, 当前算符: D_(0, 3) * D_(27, 28), 耗时: 5.967s
[2025-07-23 19:30:26] y方向算符进度: 31/64, 当前算符: D_(0, 3) * D_(28, 31), 耗时: 6.004s
[2025-07-23 19:30:32] y方向算符进度: 32/64, 当前算符: D_(0, 3) * D_(29, 30), 耗时: 6.013s
[2025-07-23 19:30:38] y方向算符进度: 33/64, 当前算符: D_(0, 3) * D_(32, 35), 耗时: 6.013s
[2025-07-23 19:30:44] y方向算符进度: 34/64, 当前算符: D_(0, 3) * D_(32, 47), 耗时: 5.978s
[2025-07-23 19:30:50] y方向算符进度: 35/64, 当前算符: D_(0, 3) * D_(33, 34), 耗时: 5.977s
[2025-07-23 19:30:56] y方向算符进度: 36/64, 当前算符: D_(0, 3) * D_(33, 46), 耗时: 5.967s
[2025-07-23 19:31:02] y方向算符进度: 37/64, 当前算符: D_(0, 3) * D_(34, 37), 耗时: 6.051s
[2025-07-23 19:31:08] y方向算符进度: 38/64, 当前算符: D_(0, 3) * D_(35, 36), 耗时: 6.009s
[2025-07-23 19:31:14] y方向算符进度: 39/64, 当前算符: D_(0, 3) * D_(36, 39), 耗时: 5.971s
[2025-07-23 19:31:20] y方向算符进度: 40/64, 当前算符: D_(0, 3) * D_(37, 38), 耗时: 5.968s
[2025-07-23 19:31:26] y方向算符进度: 41/64, 当前算符: D_(0, 3) * D_(38, 41), 耗时: 5.968s
[2025-07-23 19:31:32] y方向算符进度: 42/64, 当前算符: D_(0, 3) * D_(39, 40), 耗时: 6.078s
[2025-07-23 19:31:38] y方向算符进度: 43/64, 当前算符: D_(0, 3) * D_(40, 43), 耗时: 6.004s
[2025-07-23 19:31:44] y方向算符进度: 44/64, 当前算符: D_(0, 3) * D_(41, 42), 耗时: 6.014s
[2025-07-23 19:31:50] y方向算符进度: 45/64, 当前算符: D_(0, 3) * D_(42, 45), 耗时: 5.968s
[2025-07-23 19:31:56] y方向算符进度: 46/64, 当前算符: D_(0, 3) * D_(43, 44), 耗时: 5.971s
[2025-07-23 19:32:02] y方向算符进度: 47/64, 当前算符: D_(0, 3) * D_(44, 47), 耗时: 6.031s
[2025-07-23 19:32:08] y方向算符进度: 48/64, 当前算符: D_(0, 3) * D_(45, 46), 耗时: 6.009s
[2025-07-23 19:32:14] y方向算符进度: 49/64, 当前算符: D_(0, 3) * D_(48, 51), 耗时: 6.013s
[2025-07-23 19:32:20] y方向算符进度: 50/64, 当前算符: D_(0, 3) * D_(48, 63), 耗时: 5.979s
[2025-07-23 19:32:26] y方向算符进度: 51/64, 当前算符: D_(0, 3) * D_(49, 50), 耗时: 5.981s
[2025-07-23 19:32:32] y方向算符进度: 52/64, 当前算符: D_(0, 3) * D_(49, 62), 耗时: 6.010s
[2025-07-23 19:32:38] y方向算符进度: 53/64, 当前算符: D_(0, 3) * D_(50, 53), 耗时: 6.015s
[2025-07-23 19:32:44] y方向算符进度: 54/64, 当前算符: D_(0, 3) * D_(51, 52), 耗时: 6.004s
[2025-07-23 19:32:50] y方向算符进度: 55/64, 当前算符: D_(0, 3) * D_(52, 55), 耗时: 5.967s
[2025-07-23 19:32:56] y方向算符进度: 56/64, 当前算符: D_(0, 3) * D_(53, 54), 耗时: 5.967s
[2025-07-23 19:33:02] y方向算符进度: 57/64, 当前算符: D_(0, 3) * D_(54, 57), 耗时: 6.048s
[2025-07-23 19:33:08] y方向算符进度: 58/64, 当前算符: D_(0, 3) * D_(55, 56), 耗时: 6.006s
[2025-07-23 19:33:14] y方向算符进度: 59/64, 当前算符: D_(0, 3) * D_(56, 59), 耗时: 6.004s
[2025-07-23 19:33:20] y方向算符进度: 60/64, 当前算符: D_(0, 3) * D_(57, 58), 耗时: 6.016s
[2025-07-23 19:33:26] y方向算符进度: 61/64, 当前算符: D_(0, 3) * D_(58, 61), 耗时: 5.967s
[2025-07-23 19:33:32] y方向算符进度: 62/64, 当前算符: D_(0, 3) * D_(59, 60), 耗时: 6.000s
[2025-07-23 19:33:38] y方向算符进度: 63/64, 当前算符: D_(0, 3) * D_(60, 63), 耗时: 6.004s
[2025-07-23 19:33:44] y方向算符进度: 64/64, 当前算符: D_(0, 3) * D_(61, 62), 耗时: 6.011s
[2025-07-23 19:33:44] y方向二聚体相关函数计算完成,耗时: 378.90 秒
[2025-07-23 19:33:44] 计算傅里叶变换...
[2025-07-23 19:33:44] 二聚体结构因子计算完成
[2025-07-23 19:33:45] 二聚体相关函数平均误差: 0.000490
[2025-07-23 19:33:45] 恢复原始样本数: 4096
[2025-07-23 19:33:45] ================================================================================
[2025-07-23 19:33:45] 所有分析完成
