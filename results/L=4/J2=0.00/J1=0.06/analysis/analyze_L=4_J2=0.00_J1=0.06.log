[2025-07-23 19:52:22] ================================================================================
[2025-07-23 19:52:22] 加载量子态: L=4, J2=0.00, J1=0.06
[2025-07-23 19:52:22] 设置样本数为: 1048576
[2025-07-23 19:52:22] 开始生成共享样本集...
[2025-07-23 19:53:32] 样本生成完成,耗时: 70.394 秒
[2025-07-23 19:53:32] ================================================================================
[2025-07-23 19:53:32] 开始计算自旋结构因子...
[2025-07-23 19:53:32] 初始化操作符缓存...
[2025-07-23 19:53:32] 预构建所有自旋相关操作符...
[2025-07-23 19:53:32] 开始计算自旋相关函数...
[2025-07-23 19:53:41] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 8.582s
[2025-07-23 19:53:50] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 9.162s
[2025-07-23 19:53:54] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 3.595s
[2025-07-23 19:53:57] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 3.595s
[2025-07-23 19:54:01] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 3.596s
[2025-07-23 19:54:04] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 3.592s
[2025-07-23 19:54:08] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 3.598s
[2025-07-23 19:54:12] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 3.595s
[2025-07-23 19:54:15] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 3.576s
[2025-07-23 19:54:19] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 3.598s
[2025-07-23 19:54:22] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 3.595s
[2025-07-23 19:54:26] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 3.592s
[2025-07-23 19:54:30] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 3.599s
[2025-07-23 19:54:33] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 3.579s
[2025-07-23 19:54:37] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 3.598s
[2025-07-23 19:54:40] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 3.595s
[2025-07-23 19:54:44] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 3.593s
[2025-07-23 19:54:48] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 3.578s
[2025-07-23 19:54:51] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 3.599s
[2025-07-23 19:54:55] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 3.593s
[2025-07-23 19:54:58] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 3.592s
[2025-07-23 19:55:02] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 3.592s
[2025-07-23 19:55:06] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 3.593s
[2025-07-23 19:55:09] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 3.579s
[2025-07-23 19:55:13] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 3.596s
[2025-07-23 19:55:16] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 3.599s
[2025-07-23 19:55:20] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 3.593s
[2025-07-23 19:55:24] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 3.597s
[2025-07-23 19:55:27] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 3.578s
[2025-07-23 19:55:31] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 3.579s
[2025-07-23 19:55:34] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 3.599s
[2025-07-23 19:55:38] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 3.591s
[2025-07-23 19:55:41] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 3.596s
[2025-07-23 19:55:45] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 3.600s
[2025-07-23 19:55:49] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 3.584s
[2025-07-23 19:55:52] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 3.601s
[2025-07-23 19:55:56] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 3.591s
[2025-07-23 19:55:59] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 3.592s
[2025-07-23 19:56:03] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 3.600s
[2025-07-23 19:56:07] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 3.583s
[2025-07-23 19:56:10] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 3.600s
[2025-07-23 19:56:14] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 3.600s
[2025-07-23 19:56:17] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 3.598s
[2025-07-23 19:56:21] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 3.591s
[2025-07-23 19:56:25] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 3.594s
[2025-07-23 19:56:28] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 3.597s
[2025-07-23 19:56:32] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 3.582s
[2025-07-23 19:56:35] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 3.599s
[2025-07-23 19:56:39] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 3.582s
[2025-07-23 19:56:43] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 3.594s
[2025-07-23 19:56:46] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 3.601s
[2025-07-23 19:56:50] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 3.593s
[2025-07-23 19:56:53] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 3.600s
[2025-07-23 19:56:57] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 3.598s
[2025-07-23 19:57:01] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 3.578s
[2025-07-23 19:57:04] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 3.601s
[2025-07-23 19:57:08] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 3.592s
[2025-07-23 19:57:11] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 3.587s
[2025-07-23 19:57:15] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 3.600s
[2025-07-23 19:57:19] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 3.577s
[2025-07-23 19:57:22] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 3.598s
[2025-07-23 19:57:26] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 3.602s
[2025-07-23 19:57:29] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 3.594s
[2025-07-23 19:57:33] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 3.585s
[2025-07-23 19:57:33] 自旋相关函数计算完成,总耗时 240.57 秒
[2025-07-23 19:57:33] 计算傅里叶变换...
[2025-07-23 19:57:34] 自旋结构因子计算完成
[2025-07-23 19:57:35] 自旋相关函数平均误差: 0.000641
[2025-07-23 19:57:35] ================================================================================
[2025-07-23 19:57:35] 开始计算二聚体结构因子...
[2025-07-23 19:57:35] 识别x和y方向的二聚体...
[2025-07-23 19:57:35] 找到 64 个x方向二聚体和 64 个y方向二聚体
[2025-07-23 19:57:35] 预计算二聚体操作符...
[2025-07-23 19:57:35] 使用x-二聚体 (0, 1) (全局索引 0) 作为参考点, 计算x方向连通二聚体相关函数...
[2025-07-23 19:57:39] x方向算符进度: 1/64, 当前算符: D_(0, 1) * D_(0, 1), 耗时: 3.644s
[2025-07-23 19:57:54] x方向算符进度: 2/64, 当前算符: D_(0, 1) * D_(0, 49), 耗时: 14.644s
[2025-07-23 19:57:59] x方向算符进度: 3/64, 当前算符: D_(0, 1) * D_(1, 16), 耗时: 4.727s
[2025-07-23 19:58:10] x方向算符进度: 4/64, 当前算符: D_(0, 1) * D_(2, 3), 耗时: 11.624s
[2025-07-23 19:58:16] x方向算符进度: 5/64, 当前算符: D_(0, 1) * D_(2, 19), 耗时: 5.998s
[2025-07-23 19:58:22] x方向算符进度: 6/64, 当前算符: D_(0, 1) * D_(3, 50), 耗时: 6.003s
[2025-07-23 19:58:28] x方向算符进度: 7/64, 当前算符: D_(0, 1) * D_(4, 5), 耗时: 6.015s
[2025-07-23 19:58:34] x方向算符进度: 8/64, 当前算符: D_(0, 1) * D_(4, 53), 耗时: 5.991s
[2025-07-23 19:58:40] x方向算符进度: 9/64, 当前算符: D_(0, 1) * D_(5, 20), 耗时: 6.016s
[2025-07-23 19:58:46] x方向算符进度: 10/64, 当前算符: D_(0, 1) * D_(6, 7), 耗时: 5.997s
[2025-07-23 19:58:52] x方向算符进度: 11/64, 当前算符: D_(0, 1) * D_(6, 23), 耗时: 6.018s
[2025-07-23 19:58:58] x方向算符进度: 12/64, 当前算符: D_(0, 1) * D_(7, 54), 耗时: 6.000s
[2025-07-23 19:59:04] x方向算符进度: 13/64, 当前算符: D_(0, 1) * D_(8, 9), 耗时: 6.019s
[2025-07-23 19:59:10] x方向算符进度: 14/64, 当前算符: D_(0, 1) * D_(8, 57), 耗时: 5.991s
[2025-07-23 19:59:16] x方向算符进度: 15/64, 当前算符: D_(0, 1) * D_(9, 24), 耗时: 6.000s
[2025-07-23 19:59:22] x方向算符进度: 16/64, 当前算符: D_(0, 1) * D_(10, 11), 耗时: 6.013s
[2025-07-23 19:59:28] x方向算符进度: 17/64, 当前算符: D_(0, 1) * D_(10, 27), 耗时: 5.997s
[2025-07-23 19:59:34] x方向算符进度: 18/64, 当前算符: D_(0, 1) * D_(11, 58), 耗时: 5.975s
[2025-07-23 19:59:40] x方向算符进度: 19/64, 当前算符: D_(0, 1) * D_(12, 13), 耗时: 6.015s
[2025-07-23 19:59:46] x方向算符进度: 20/64, 当前算符: D_(0, 1) * D_(12, 61), 耗时: 5.984s
[2025-07-23 19:59:52] x方向算符进度: 21/64, 当前算符: D_(0, 1) * D_(13, 28), 耗时: 6.011s
[2025-07-23 19:59:58] x方向算符进度: 22/64, 当前算符: D_(0, 1) * D_(14, 15), 耗时: 6.000s
[2025-07-23 20:00:04] x方向算符进度: 23/64, 当前算符: D_(0, 1) * D_(14, 31), 耗时: 6.017s
[2025-07-23 20:00:10] x方向算符进度: 24/64, 当前算符: D_(0, 1) * D_(15, 62), 耗时: 5.976s
[2025-07-23 20:00:16] x方向算符进度: 25/64, 当前算符: D_(0, 1) * D_(16, 17), 耗时: 6.016s
[2025-07-23 20:00:22] x方向算符进度: 26/64, 当前算符: D_(0, 1) * D_(17, 32), 耗时: 5.995s
[2025-07-23 20:00:28] x方向算符进度: 27/64, 当前算符: D_(0, 1) * D_(18, 19), 耗时: 6.017s
[2025-07-23 20:00:34] x方向算符进度: 28/64, 当前算符: D_(0, 1) * D_(18, 35), 耗时: 5.995s
[2025-07-23 20:00:40] x方向算符进度: 29/64, 当前算符: D_(0, 1) * D_(20, 21), 耗时: 6.015s
[2025-07-23 20:00:46] x方向算符进度: 30/64, 当前算符: D_(0, 1) * D_(21, 36), 耗时: 5.975s
[2025-07-23 20:00:52] x方向算符进度: 31/64, 当前算符: D_(0, 1) * D_(22, 23), 耗时: 5.999s
[2025-07-23 20:00:58] x方向算符进度: 32/64, 当前算符: D_(0, 1) * D_(22, 39), 耗时: 6.019s
[2025-07-23 20:01:04] x方向算符进度: 33/64, 当前算符: D_(0, 1) * D_(24, 25), 耗时: 5.995s
[2025-07-23 20:01:10] x方向算符进度: 34/64, 当前算符: D_(0, 1) * D_(25, 40), 耗时: 6.017s
[2025-07-23 20:01:16] x方向算符进度: 35/64, 当前算符: D_(0, 1) * D_(26, 27), 耗时: 6.015s
[2025-07-23 20:01:22] x方向算符进度: 36/64, 当前算符: D_(0, 1) * D_(26, 43), 耗时: 5.986s
[2025-07-23 20:01:28] x方向算符进度: 37/64, 当前算符: D_(0, 1) * D_(28, 29), 耗时: 6.020s
[2025-07-23 20:01:34] x方向算符进度: 38/64, 当前算符: D_(0, 1) * D_(29, 44), 耗时: 6.000s
[2025-07-23 20:01:40] x方向算符进度: 39/64, 当前算符: D_(0, 1) * D_(30, 31), 耗时: 6.021s
[2025-07-23 20:01:46] x方向算符进度: 40/64, 当前算符: D_(0, 1) * D_(30, 47), 耗时: 5.981s
[2025-07-23 20:01:52] x方向算符进度: 41/64, 当前算符: D_(0, 1) * D_(32, 33), 耗时: 6.023s
[2025-07-23 20:01:58] x方向算符进度: 42/64, 当前算符: D_(0, 1) * D_(33, 48), 耗时: 5.997s
[2025-07-23 20:02:04] x方向算符进度: 43/64, 当前算符: D_(0, 1) * D_(34, 35), 耗时: 6.011s
[2025-07-23 20:02:10] x方向算符进度: 44/64, 当前算符: D_(0, 1) * D_(34, 51), 耗时: 5.997s
[2025-07-23 20:02:16] x方向算符进度: 45/64, 当前算符: D_(0, 1) * D_(36, 37), 耗时: 6.014s
[2025-07-23 20:02:22] x方向算符进度: 46/64, 当前算符: D_(0, 1) * D_(37, 52), 耗时: 5.978s
[2025-07-23 20:02:28] x方向算符进度: 47/64, 当前算符: D_(0, 1) * D_(38, 39), 耗时: 5.985s
[2025-07-23 20:02:34] x方向算符进度: 48/64, 当前算符: D_(0, 1) * D_(38, 55), 耗时: 6.013s
[2025-07-23 20:02:40] x方向算符进度: 49/64, 当前算符: D_(0, 1) * D_(40, 41), 耗时: 5.981s
[2025-07-23 20:02:46] x方向算符进度: 50/64, 当前算符: D_(0, 1) * D_(41, 56), 耗时: 6.015s
[2025-07-23 20:02:52] x方向算符进度: 51/64, 当前算符: D_(0, 1) * D_(42, 43), 耗时: 5.995s
[2025-07-23 20:02:58] x方向算符进度: 52/64, 当前算符: D_(0, 1) * D_(42, 59), 耗时: 6.000s
[2025-07-23 20:03:04] x方向算符进度: 53/64, 当前算符: D_(0, 1) * D_(44, 45), 耗时: 6.018s
[2025-07-23 20:03:10] x方向算符进度: 54/64, 当前算符: D_(0, 1) * D_(45, 60), 耗时: 5.991s
[2025-07-23 20:03:16] x方向算符进度: 55/64, 当前算符: D_(0, 1) * D_(46, 47), 耗时: 6.021s
[2025-07-23 20:03:22] x方向算符进度: 56/64, 当前算符: D_(0, 1) * D_(46, 63), 耗时: 5.999s
[2025-07-23 20:03:28] x方向算符进度: 57/64, 当前算符: D_(0, 1) * D_(48, 49), 耗时: 6.020s
[2025-07-23 20:03:34] x方向算符进度: 58/64, 当前算符: D_(0, 1) * D_(50, 51), 耗时: 5.980s
[2025-07-23 20:03:40] x方向算符进度: 59/64, 当前算符: D_(0, 1) * D_(52, 53), 耗时: 6.024s
[2025-07-23 20:03:46] x方向算符进度: 60/64, 当前算符: D_(0, 1) * D_(54, 55), 耗时: 5.997s
[2025-07-23 20:03:52] x方向算符进度: 61/64, 当前算符: D_(0, 1) * D_(56, 57), 耗时: 6.024s
[2025-07-23 20:03:58] x方向算符进度: 62/64, 当前算符: D_(0, 1) * D_(58, 59), 耗时: 5.995s
[2025-07-23 20:04:04] x方向算符进度: 63/64, 当前算符: D_(0, 1) * D_(60, 61), 耗时: 5.981s
[2025-07-23 20:04:10] x方向算符进度: 64/64, 当前算符: D_(0, 1) * D_(62, 63), 耗时: 6.018s
[2025-07-23 20:04:10] x方向二聚体相关函数计算完成,耗时: 394.89 秒
[2025-07-23 20:04:10] --------------------------------------------------------------------------------
[2025-07-23 20:04:10] 使用y-二聚体 (0, 3) (全局索引 64) 作为参考点, 计算y方向连通二聚体相关函数...
[2025-07-23 20:04:14] y方向算符进度: 1/64, 当前算符: D_(0, 3) * D_(0, 3), 耗时: 3.592s
[2025-07-23 20:04:19] y方向算符进度: 2/64, 当前算符: D_(0, 3) * D_(0, 15), 耗时: 4.721s
[2025-07-23 20:04:25] y方向算符进度: 3/64, 当前算符: D_(0, 3) * D_(1, 2), 耗时: 6.005s
[2025-07-23 20:04:31] y方向算符进度: 4/64, 当前算符: D_(0, 3) * D_(1, 14), 耗时: 6.028s
[2025-07-23 20:04:37] y方向算符进度: 5/64, 当前算符: D_(0, 3) * D_(2, 5), 耗时: 6.027s
[2025-07-23 20:04:41] y方向算符进度: 6/64, 当前算符: D_(0, 3) * D_(3, 4), 耗时: 4.720s
[2025-07-23 20:04:47] y方向算符进度: 7/64, 当前算符: D_(0, 3) * D_(4, 7), 耗时: 6.007s
[2025-07-23 20:04:54] y方向算符进度: 8/64, 当前算符: D_(0, 3) * D_(5, 6), 耗时: 6.019s
[2025-07-23 20:05:00] y方向算符进度: 9/64, 当前算符: D_(0, 3) * D_(6, 9), 耗时: 6.007s
[2025-07-23 20:05:06] y方向算符进度: 10/64, 当前算符: D_(0, 3) * D_(7, 8), 耗时: 6.027s
[2025-07-23 20:05:12] y方向算符进度: 11/64, 当前算符: D_(0, 3) * D_(8, 11), 耗时: 5.981s
[2025-07-23 20:05:18] y方向算符进度: 12/64, 当前算符: D_(0, 3) * D_(9, 10), 耗时: 6.023s
[2025-07-23 20:05:24] y方向算符进度: 13/64, 当前算符: D_(0, 3) * D_(10, 13), 耗时: 6.004s
[2025-07-23 20:05:30] y方向算符进度: 14/64, 当前算符: D_(0, 3) * D_(11, 12), 耗时: 6.021s
[2025-07-23 20:05:36] y方向算符进度: 15/64, 当前算符: D_(0, 3) * D_(12, 15), 耗时: 6.001s
[2025-07-23 20:05:42] y方向算符进度: 16/64, 当前算符: D_(0, 3) * D_(13, 14), 耗时: 6.020s
[2025-07-23 20:05:48] y方向算符进度: 17/64, 当前算符: D_(0, 3) * D_(16, 19), 耗时: 5.992s
[2025-07-23 20:05:54] y方向算符进度: 18/64, 当前算符: D_(0, 3) * D_(16, 31), 耗时: 6.024s
[2025-07-23 20:06:00] y方向算符进度: 19/64, 当前算符: D_(0, 3) * D_(17, 18), 耗时: 6.004s
[2025-07-23 20:06:06] y方向算符进度: 20/64, 当前算符: D_(0, 3) * D_(17, 30), 耗时: 6.031s
[2025-07-23 20:06:12] y方向算符进度: 21/64, 当前算符: D_(0, 3) * D_(18, 21), 耗时: 5.984s
[2025-07-23 20:06:18] y方向算符进度: 22/64, 当前算符: D_(0, 3) * D_(19, 20), 耗时: 6.006s
[2025-07-23 20:06:24] y方向算符进度: 23/64, 当前算符: D_(0, 3) * D_(20, 23), 耗时: 6.002s
[2025-07-23 20:06:30] y方向算符进度: 24/64, 当前算符: D_(0, 3) * D_(21, 22), 耗时: 6.030s
[2025-07-23 20:06:36] y方向算符进度: 25/64, 当前算符: D_(0, 3) * D_(22, 25), 耗时: 6.008s
[2025-07-23 20:06:42] y方向算符进度: 26/64, 当前算符: D_(0, 3) * D_(23, 24), 耗时: 6.029s
[2025-07-23 20:06:48] y方向算符进度: 27/64, 当前算符: D_(0, 3) * D_(24, 27), 耗时: 6.006s
[2025-07-23 20:06:54] y方向算符进度: 28/64, 当前算符: D_(0, 3) * D_(25, 26), 耗时: 6.029s
[2025-07-23 20:07:00] y方向算符进度: 29/64, 当前算符: D_(0, 3) * D_(26, 29), 耗时: 5.998s
[2025-07-23 20:07:06] y方向算符进度: 30/64, 当前算符: D_(0, 3) * D_(27, 28), 耗时: 6.027s
[2025-07-23 20:07:12] y方向算符进度: 31/64, 当前算符: D_(0, 3) * D_(28, 31), 耗时: 6.004s
[2025-07-23 20:07:18] y方向算符进度: 32/64, 当前算符: D_(0, 3) * D_(29, 30), 耗时: 6.028s
[2025-07-23 20:07:24] y方向算符进度: 33/64, 当前算符: D_(0, 3) * D_(32, 35), 耗时: 6.007s
[2025-07-23 20:07:30] y方向算符进度: 34/64, 当前算符: D_(0, 3) * D_(32, 47), 耗时: 6.028s
[2025-07-23 20:07:36] y方向算符进度: 35/64, 当前算符: D_(0, 3) * D_(33, 34), 耗时: 5.986s
[2025-07-23 20:07:42] y方向算符进度: 36/64, 当前算符: D_(0, 3) * D_(33, 46), 耗时: 6.027s
[2025-07-23 20:07:48] y方向算符进度: 37/64, 当前算符: D_(0, 3) * D_(34, 37), 耗时: 6.008s
[2025-07-23 20:07:54] y方向算符进度: 38/64, 当前算符: D_(0, 3) * D_(35, 36), 耗时: 6.005s
[2025-07-23 20:08:00] y方向算符进度: 39/64, 当前算符: D_(0, 3) * D_(36, 39), 耗时: 6.028s
[2025-07-23 20:08:06] y方向算符进度: 40/64, 当前算符: D_(0, 3) * D_(37, 38), 耗时: 6.026s
[2025-07-23 20:08:12] y方向算符进度: 41/64, 当前算符: D_(0, 3) * D_(38, 41), 耗时: 5.979s
[2025-07-23 20:08:18] y方向算符进度: 42/64, 当前算符: D_(0, 3) * D_(39, 40), 耗时: 6.024s
[2025-07-23 20:08:24] y方向算符进度: 43/64, 当前算符: D_(0, 3) * D_(40, 43), 耗时: 6.003s
[2025-07-23 20:08:30] y方向算符进度: 44/64, 当前算符: D_(0, 3) * D_(41, 42), 耗时: 6.028s
[2025-07-23 20:08:36] y方向算符进度: 45/64, 当前算符: D_(0, 3) * D_(42, 45), 耗时: 5.988s
[2025-07-23 20:08:42] y方向算符进度: 46/64, 当前算符: D_(0, 3) * D_(43, 44), 耗时: 6.029s
[2025-07-23 20:08:48] y方向算符进度: 47/64, 当前算符: D_(0, 3) * D_(44, 47), 耗时: 6.003s
[2025-07-23 20:08:54] y方向算符进度: 48/64, 当前算符: D_(0, 3) * D_(45, 46), 耗时: 6.027s
[2025-07-23 20:09:00] y方向算符进度: 49/64, 当前算符: D_(0, 3) * D_(48, 51), 耗时: 6.008s
[2025-07-23 20:09:06] y方向算符进度: 50/64, 当前算符: D_(0, 3) * D_(48, 63), 耗时: 6.037s
[2025-07-23 20:09:12] y方向算符进度: 51/64, 当前算符: D_(0, 3) * D_(49, 50), 耗时: 5.986s
[2025-07-23 20:09:18] y方向算符进度: 52/64, 当前算符: D_(0, 3) * D_(49, 62), 耗时: 6.029s
[2025-07-23 20:09:24] y方向算符进度: 53/64, 当前算符: D_(0, 3) * D_(50, 53), 耗时: 6.008s
[2025-07-23 20:09:30] y方向算符进度: 54/64, 当前算符: D_(0, 3) * D_(51, 52), 耗时: 6.003s
[2025-07-23 20:09:36] y方向算符进度: 55/64, 当前算符: D_(0, 3) * D_(52, 55), 耗时: 6.025s
[2025-07-23 20:09:42] y方向算符进度: 56/64, 当前算符: D_(0, 3) * D_(53, 54), 耗时: 5.992s
[2025-07-23 20:09:48] y方向算符进度: 57/64, 当前算符: D_(0, 3) * D_(54, 57), 耗时: 5.985s
[2025-07-23 20:09:54] y方向算符进度: 58/64, 当前算符: D_(0, 3) * D_(55, 56), 耗时: 6.025s
[2025-07-23 20:10:00] y方向算符进度: 59/64, 当前算符: D_(0, 3) * D_(56, 59), 耗时: 6.004s
[2025-07-23 20:10:06] y方向算符进度: 60/64, 当前算符: D_(0, 3) * D_(57, 58), 耗时: 6.020s
[2025-07-23 20:10:12] y方向算符进度: 61/64, 当前算符: D_(0, 3) * D_(58, 61), 耗时: 5.978s
[2025-07-23 20:10:18] y方向算符进度: 62/64, 当前算符: D_(0, 3) * D_(59, 60), 耗时: 6.026s
[2025-07-23 20:10:24] y方向算符进度: 63/64, 当前算符: D_(0, 3) * D_(60, 63), 耗时: 6.005s
[2025-07-23 20:10:30] y方向算符进度: 64/64, 当前算符: D_(0, 3) * D_(61, 62), 耗时: 6.023s
[2025-07-23 20:10:30] y方向二聚体相关函数计算完成,耗时: 379.83 秒
[2025-07-23 20:10:30] 计算傅里叶变换...
[2025-07-23 20:10:31] 二聚体结构因子计算完成
[2025-07-23 20:10:32] 二聚体相关函数平均误差: 0.000489
[2025-07-23 20:10:32] 恢复原始样本数: 4096
[2025-07-23 20:10:32] ================================================================================
[2025-07-23 20:10:32] 所有分析完成
