[2025-07-23 20:29:12] ================================================================================
[2025-07-23 20:29:12] 加载量子态: L=4, J2=0.00, J1=0.08
[2025-07-23 20:29:12] 设置样本数为: 1048576
[2025-07-23 20:29:12] 开始生成共享样本集...
[2025-07-23 20:30:22] 样本生成完成,耗时: 70.151 秒
[2025-07-23 20:30:22] ================================================================================
[2025-07-23 20:30:22] 开始计算自旋结构因子...
[2025-07-23 20:30:22] 初始化操作符缓存...
[2025-07-23 20:30:22] 预构建所有自旋相关操作符...
[2025-07-23 20:30:22] 开始计算自旋相关函数...
[2025-07-23 20:30:31] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 8.622s
[2025-07-23 20:30:40] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 9.197s
[2025-07-23 20:30:44] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 3.604s
[2025-07-23 20:30:47] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 3.603s
[2025-07-23 20:30:51] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 3.604s
[2025-07-23 20:30:54] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 3.599s
[2025-07-23 20:30:58] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 3.600s
[2025-07-23 20:31:02] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 3.634s
[2025-07-23 20:31:05] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 3.579s
[2025-07-23 20:31:09] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 3.600s
[2025-07-23 20:31:12] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 3.603s
[2025-07-23 20:31:16] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 3.600s
[2025-07-23 20:31:20] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 3.581s
[2025-07-23 20:31:23] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 3.578s
[2025-07-23 20:31:27] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 3.579s
[2025-07-23 20:31:30] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 3.600s
[2025-07-23 20:31:34] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 3.601s
[2025-07-23 20:31:38] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 3.580s
[2025-07-23 20:31:41] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 3.579s
[2025-07-23 20:31:45] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 3.580s
[2025-07-23 20:31:48] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 3.599s
[2025-07-23 20:31:52] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 3.599s
[2025-07-23 20:31:56] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 3.599s
[2025-07-23 20:31:59] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 3.579s
[2025-07-23 20:32:03] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 3.578s
[2025-07-23 20:32:06] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 3.600s
[2025-07-23 20:32:10] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 3.599s
[2025-07-23 20:32:14] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 3.599s
[2025-07-23 20:32:17] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 3.578s
[2025-07-23 20:32:21] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 3.579s
[2025-07-23 20:32:24] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 3.578s
[2025-07-23 20:32:28] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 3.598s
[2025-07-23 20:32:31] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 3.600s
[2025-07-23 20:32:35] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 3.577s
[2025-07-23 20:32:39] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 3.579s
[2025-07-23 20:32:42] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 3.579s
[2025-07-23 20:32:46] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 3.599s
[2025-07-23 20:32:49] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 3.599s
[2025-07-23 20:32:53] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 3.599s
[2025-07-23 20:32:57] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 3.577s
[2025-07-23 20:33:00] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 3.578s
[2025-07-23 20:33:04] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 3.600s
[2025-07-23 20:33:07] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 3.597s
[2025-07-23 20:33:11] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 3.597s
[2025-07-23 20:33:15] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 3.577s
[2025-07-23 20:33:18] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 3.577s
[2025-07-23 20:33:22] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 3.580s
[2025-07-23 20:33:25] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 3.579s
[2025-07-23 20:33:29] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 3.579s
[2025-07-23 20:33:32] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 3.601s
[2025-07-23 20:33:36] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 3.602s
[2025-07-23 20:33:40] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 3.601s
[2025-07-23 20:33:43] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 3.581s
[2025-07-23 20:33:47] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 3.581s
[2025-07-23 20:33:50] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 3.578s
[2025-07-23 20:33:54] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 3.599s
[2025-07-23 20:33:58] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 3.600s
[2025-07-23 20:34:01] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 3.581s
[2025-07-23 20:34:05] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 3.580s
[2025-07-23 20:34:08] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 3.580s
[2025-07-23 20:34:12] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 3.600s
[2025-07-23 20:34:16] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 3.599s
[2025-07-23 20:34:19] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 3.603s
[2025-07-23 20:34:23] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 3.577s
[2025-07-23 20:34:23] 自旋相关函数计算完成,总耗时 240.53 秒
[2025-07-23 20:34:23] 计算傅里叶变换...
[2025-07-23 20:34:23] 自旋结构因子计算完成
[2025-07-23 20:34:24] 自旋相关函数平均误差: 0.000634
[2025-07-23 20:34:24] ================================================================================
[2025-07-23 20:34:24] 开始计算二聚体结构因子...
[2025-07-23 20:34:24] 识别x和y方向的二聚体...
[2025-07-23 20:34:24] 找到 64 个x方向二聚体和 64 个y方向二聚体
[2025-07-23 20:34:24] 预计算二聚体操作符...
[2025-07-23 20:34:25] 使用x-二聚体 (0, 1) (全局索引 0) 作为参考点, 计算x方向连通二聚体相关函数...
[2025-07-23 20:34:29] x方向算符进度: 1/64, 当前算符: D_(0, 1) * D_(0, 1), 耗时: 3.613s
[2025-07-23 20:34:44] x方向算符进度: 2/64, 当前算符: D_(0, 1) * D_(0, 49), 耗时: 14.748s
[2025-07-23 20:34:48] x方向算符进度: 3/64, 当前算符: D_(0, 1) * D_(1, 16), 耗时: 4.746s
[2025-07-23 20:35:00] x方向算符进度: 4/64, 当前算符: D_(0, 1) * D_(2, 3), 耗时: 11.588s
[2025-07-23 20:35:06] x方向算符进度: 5/64, 当前算符: D_(0, 1) * D_(2, 19), 耗时: 6.015s
[2025-07-23 20:35:12] x方向算符进度: 6/64, 当前算符: D_(0, 1) * D_(3, 50), 耗时: 6.018s
[2025-07-23 20:35:18] x方向算符进度: 7/64, 当前算符: D_(0, 1) * D_(4, 5), 耗时: 5.974s
[2025-07-23 20:35:24] x方向算符进度: 8/64, 当前算符: D_(0, 1) * D_(4, 53), 耗时: 5.973s
[2025-07-23 20:35:30] x方向算符进度: 9/64, 当前算符: D_(0, 1) * D_(5, 20), 耗时: 6.017s
[2025-07-23 20:35:36] x方向算符进度: 10/64, 当前算符: D_(0, 1) * D_(6, 7), 耗时: 6.013s
[2025-07-23 20:35:42] x方向算符进度: 11/64, 当前算符: D_(0, 1) * D_(6, 23), 耗时: 6.018s
[2025-07-23 20:35:48] x方向算符进度: 12/64, 当前算符: D_(0, 1) * D_(7, 54), 耗时: 6.015s
[2025-07-23 20:35:54] x方向算符进度: 13/64, 当前算符: D_(0, 1) * D_(8, 9), 耗时: 5.973s
[2025-07-23 20:36:00] x方向算符进度: 14/64, 当前算符: D_(0, 1) * D_(8, 57), 耗时: 5.974s
[2025-07-23 20:36:06] x方向算符进度: 15/64, 当前算符: D_(0, 1) * D_(9, 24), 耗时: 6.015s
[2025-07-23 20:36:12] x方向算符进度: 16/64, 当前算符: D_(0, 1) * D_(10, 11), 耗时: 6.018s
[2025-07-23 20:36:18] x方向算符进度: 17/64, 当前算符: D_(0, 1) * D_(10, 27), 耗时: 6.013s
[2025-07-23 20:36:24] x方向算符进度: 18/64, 当前算符: D_(0, 1) * D_(11, 58), 耗时: 5.974s
[2025-07-23 20:36:30] x方向算符进度: 19/64, 当前算符: D_(0, 1) * D_(12, 13), 耗时: 5.972s
[2025-07-23 20:36:36] x方向算符进度: 20/64, 当前算符: D_(0, 1) * D_(12, 61), 耗时: 5.974s
[2025-07-23 20:36:42] x方向算符进度: 21/64, 当前算符: D_(0, 1) * D_(13, 28), 耗时: 6.017s
[2025-07-23 20:36:48] x方向算符进度: 22/64, 当前算符: D_(0, 1) * D_(14, 15), 耗时: 6.016s
[2025-07-23 20:36:54] x方向算符进度: 23/64, 当前算符: D_(0, 1) * D_(14, 31), 耗时: 5.974s
[2025-07-23 20:37:00] x方向算符进度: 24/64, 当前算符: D_(0, 1) * D_(15, 62), 耗时: 5.972s
[2025-07-23 20:37:06] x方向算符进度: 25/64, 当前算符: D_(0, 1) * D_(16, 17), 耗时: 5.973s
[2025-07-23 20:37:12] x方向算符进度: 26/64, 当前算符: D_(0, 1) * D_(17, 32), 耗时: 6.013s
[2025-07-23 20:37:18] x方向算符进度: 27/64, 当前算符: D_(0, 1) * D_(18, 19), 耗时: 6.018s
[2025-07-23 20:37:24] x方向算符进度: 28/64, 当前算符: D_(0, 1) * D_(18, 35), 耗时: 6.013s
[2025-07-23 20:37:30] x方向算符进度: 29/64, 当前算符: D_(0, 1) * D_(20, 21), 耗时: 5.972s
[2025-07-23 20:37:36] x方向算符进度: 30/64, 当前算符: D_(0, 1) * D_(21, 36), 耗时: 5.971s
[2025-07-23 20:37:42] x方向算符进度: 31/64, 当前算符: D_(0, 1) * D_(22, 23), 耗时: 6.015s
[2025-07-23 20:37:48] x方向算符进度: 32/64, 当前算符: D_(0, 1) * D_(22, 39), 耗时: 6.014s
[2025-07-23 20:37:54] x方向算符进度: 33/64, 当前算符: D_(0, 1) * D_(24, 25), 耗时: 6.012s
[2025-07-23 20:38:00] x方向算符进度: 34/64, 当前算符: D_(0, 1) * D_(25, 40), 耗时: 5.972s
[2025-07-23 20:38:06] x方向算符进度: 35/64, 当前算符: D_(0, 1) * D_(26, 27), 耗时: 5.972s
[2025-07-23 20:38:12] x方向算符进度: 36/64, 当前算符: D_(0, 1) * D_(26, 43), 耗时: 5.972s
[2025-07-23 20:38:18] x方向算符进度: 37/64, 当前算符: D_(0, 1) * D_(28, 29), 耗时: 6.015s
[2025-07-23 20:38:24] x方向算符进度: 38/64, 当前算符: D_(0, 1) * D_(29, 44), 耗时: 6.016s
[2025-07-23 20:38:30] x方向算符进度: 39/64, 当前算符: D_(0, 1) * D_(30, 31), 耗时: 5.973s
[2025-07-23 20:38:36] x方向算符进度: 40/64, 当前算符: D_(0, 1) * D_(30, 47), 耗时: 5.972s
[2025-07-23 20:38:42] x方向算符进度: 41/64, 当前算符: D_(0, 1) * D_(32, 33), 耗时: 5.972s
[2025-07-23 20:38:48] x方向算符进度: 42/64, 当前算符: D_(0, 1) * D_(33, 48), 耗时: 6.013s
[2025-07-23 20:38:54] x方向算符进度: 43/64, 当前算符: D_(0, 1) * D_(34, 35), 耗时: 6.012s
[2025-07-23 20:39:00] x方向算符进度: 44/64, 当前算符: D_(0, 1) * D_(34, 51), 耗时: 6.012s
[2025-07-23 20:39:06] x方向算符进度: 45/64, 当前算符: D_(0, 1) * D_(36, 37), 耗时: 5.971s
[2025-07-23 20:39:12] x方向算符进度: 46/64, 当前算符: D_(0, 1) * D_(37, 52), 耗时: 5.971s
[2025-07-23 20:39:18] x方向算符进度: 47/64, 当前算符: D_(0, 1) * D_(38, 39), 耗时: 5.972s
[2025-07-23 20:39:24] x方向算符进度: 48/64, 当前算符: D_(0, 1) * D_(38, 55), 耗时: 5.971s
[2025-07-23 20:39:30] x方向算符进度: 49/64, 当前算符: D_(0, 1) * D_(40, 41), 耗时: 5.973s
[2025-07-23 20:39:36] x方向算符进度: 50/64, 当前算符: D_(0, 1) * D_(41, 56), 耗时: 6.011s
[2025-07-23 20:39:42] x方向算符进度: 51/64, 当前算符: D_(0, 1) * D_(42, 43), 耗时: 6.011s
[2025-07-23 20:39:48] x方向算符进度: 52/64, 当前算符: D_(0, 1) * D_(42, 59), 耗时: 6.014s
[2025-07-23 20:39:54] x方向算符进度: 53/64, 当前算符: D_(0, 1) * D_(44, 45), 耗时: 5.972s
[2025-07-23 20:40:00] x方向算符进度: 54/64, 当前算符: D_(0, 1) * D_(45, 60), 耗时: 5.972s
[2025-07-23 20:40:06] x方向算符进度: 55/64, 当前算符: D_(0, 1) * D_(46, 47), 耗时: 6.014s
[2025-07-23 20:40:12] x方向算符进度: 56/64, 当前算符: D_(0, 1) * D_(46, 63), 耗时: 6.014s
[2025-07-23 20:40:18] x方向算符进度: 57/64, 当前算符: D_(0, 1) * D_(48, 49), 耗时: 6.016s
[2025-07-23 20:40:24] x方向算符进度: 58/64, 当前算符: D_(0, 1) * D_(50, 51), 耗时: 5.972s
[2025-07-23 20:40:30] x方向算符进度: 59/64, 当前算符: D_(0, 1) * D_(52, 53), 耗时: 5.972s
[2025-07-23 20:40:36] x方向算符进度: 60/64, 当前算符: D_(0, 1) * D_(54, 55), 耗时: 5.970s
[2025-07-23 20:40:42] x方向算符进度: 61/64, 当前算符: D_(0, 1) * D_(56, 57), 耗时: 6.018s
[2025-07-23 20:40:48] x方向算符进度: 62/64, 当前算符: D_(0, 1) * D_(58, 59), 耗时: 6.012s
[2025-07-23 20:40:54] x方向算符进度: 63/64, 当前算符: D_(0, 1) * D_(60, 61), 耗时: 5.972s
[2025-07-23 20:41:00] x方向算符进度: 64/64, 当前算符: D_(0, 1) * D_(62, 63), 耗时: 5.973s
[2025-07-23 20:41:00] x方向二聚体相关函数计算完成,耗时: 394.39 秒
[2025-07-23 20:41:00] --------------------------------------------------------------------------------
[2025-07-23 20:41:00] 使用y-二聚体 (0, 3) (全局索引 64) 作为参考点, 计算y方向连通二聚体相关函数...
[2025-07-23 20:41:03] y方向算符进度: 1/64, 当前算符: D_(0, 3) * D_(0, 3), 耗时: 3.597s
[2025-07-23 20:41:08] y方向算符进度: 2/64, 当前算符: D_(0, 3) * D_(0, 15), 耗时: 4.719s
[2025-07-23 20:41:14] y方向算符进度: 3/64, 当前算符: D_(0, 3) * D_(1, 2), 耗时: 6.016s
[2025-07-23 20:41:20] y方向算符进度: 4/64, 当前算符: D_(0, 3) * D_(1, 14), 耗时: 6.020s
[2025-07-23 20:41:26] y方向算符进度: 5/64, 当前算符: D_(0, 3) * D_(2, 5), 耗时: 5.979s
[2025-07-23 20:41:31] y方向算符进度: 6/64, 当前算符: D_(0, 3) * D_(3, 4), 耗时: 4.688s
[2025-07-23 20:41:37] y方向算符进度: 7/64, 当前算符: D_(0, 3) * D_(4, 7), 耗时: 6.019s
[2025-07-23 20:41:43] y方向算符进度: 8/64, 当前算符: D_(0, 3) * D_(5, 6), 耗时: 6.018s
[2025-07-23 20:41:49] y方向算符进度: 9/64, 当前算符: D_(0, 3) * D_(6, 9), 耗时: 6.018s
[2025-07-23 20:41:55] y方向算符进度: 10/64, 当前算符: D_(0, 3) * D_(7, 8), 耗时: 5.979s
[2025-07-23 20:42:01] y方向算符进度: 11/64, 当前算符: D_(0, 3) * D_(8, 11), 耗时: 5.979s
[2025-07-23 20:42:07] y方向算符进度: 12/64, 当前算符: D_(0, 3) * D_(9, 10), 耗时: 5.979s
[2025-07-23 20:42:13] y方向算符进度: 13/64, 当前算符: D_(0, 3) * D_(10, 13), 耗时: 6.018s
[2025-07-23 20:42:19] y方向算符进度: 14/64, 当前算符: D_(0, 3) * D_(11, 12), 耗时: 6.019s
[2025-07-23 20:42:25] y方向算符进度: 15/64, 当前算符: D_(0, 3) * D_(12, 15), 耗时: 5.979s
[2025-07-23 20:42:31] y方向算符进度: 16/64, 当前算符: D_(0, 3) * D_(13, 14), 耗时: 5.979s
[2025-07-23 20:42:37] y方向算符进度: 17/64, 当前算符: D_(0, 3) * D_(16, 19), 耗时: 5.979s
[2025-07-23 20:42:43] y方向算符进度: 18/64, 当前算符: D_(0, 3) * D_(16, 31), 耗时: 6.018s
[2025-07-23 20:42:49] y方向算符进度: 19/64, 当前算符: D_(0, 3) * D_(17, 18), 耗时: 6.019s
[2025-07-23 20:42:55] y方向算符进度: 20/64, 当前算符: D_(0, 3) * D_(17, 30), 耗时: 6.018s
[2025-07-23 20:43:01] y方向算符进度: 21/64, 当前算符: D_(0, 3) * D_(18, 21), 耗时: 5.980s
[2025-07-23 20:43:07] y方向算符进度: 22/64, 当前算符: D_(0, 3) * D_(19, 20), 耗时: 5.980s
[2025-07-23 20:43:13] y方向算符进度: 23/64, 当前算符: D_(0, 3) * D_(20, 23), 耗时: 6.016s
[2025-07-23 20:43:19] y方向算符进度: 24/64, 当前算符: D_(0, 3) * D_(21, 22), 耗时: 6.018s
[2025-07-23 20:43:25] y方向算符进度: 25/64, 当前算符: D_(0, 3) * D_(22, 25), 耗时: 6.018s
[2025-07-23 20:43:31] y方向算符进度: 26/64, 当前算符: D_(0, 3) * D_(23, 24), 耗时: 6.020s
[2025-07-23 20:43:37] y方向算符进度: 27/64, 当前算符: D_(0, 3) * D_(24, 27), 耗时: 6.019s
[2025-07-23 20:43:43] y方向算符进度: 28/64, 当前算符: D_(0, 3) * D_(25, 26), 耗时: 6.018s
[2025-07-23 20:43:49] y方向算符进度: 29/64, 当前算符: D_(0, 3) * D_(26, 29), 耗时: 5.979s
[2025-07-23 20:43:55] y方向算符进度: 30/64, 当前算符: D_(0, 3) * D_(27, 28), 耗时: 5.980s
[2025-07-23 20:44:01] y方向算符进度: 31/64, 当前算符: D_(0, 3) * D_(28, 31), 耗时: 6.017s
[2025-07-23 20:44:07] y方向算符进度: 32/64, 当前算符: D_(0, 3) * D_(29, 30), 耗时: 6.018s
[2025-07-23 20:44:13] y方向算符进度: 33/64, 当前算符: D_(0, 3) * D_(32, 35), 耗时: 6.018s
[2025-07-23 20:44:19] y方向算符进度: 34/64, 当前算符: D_(0, 3) * D_(32, 47), 耗时: 5.980s
[2025-07-23 20:44:25] y方向算符进度: 35/64, 当前算符: D_(0, 3) * D_(33, 34), 耗时: 5.980s
[2025-07-23 20:44:31] y方向算符进度: 36/64, 当前算符: D_(0, 3) * D_(33, 46), 耗时: 5.981s
[2025-07-23 20:44:37] y方向算符进度: 37/64, 当前算符: D_(0, 3) * D_(34, 37), 耗时: 6.018s
[2025-07-23 20:44:43] y方向算符进度: 38/64, 当前算符: D_(0, 3) * D_(35, 36), 耗时: 6.020s
[2025-07-23 20:44:49] y方向算符进度: 39/64, 当前算符: D_(0, 3) * D_(36, 39), 耗时: 5.979s
[2025-07-23 20:44:55] y方向算符进度: 40/64, 当前算符: D_(0, 3) * D_(37, 38), 耗时: 5.979s
[2025-07-23 20:45:01] y方向算符进度: 41/64, 当前算符: D_(0, 3) * D_(38, 41), 耗时: 5.980s
[2025-07-23 20:45:07] y方向算符进度: 42/64, 当前算符: D_(0, 3) * D_(39, 40), 耗时: 6.018s
[2025-07-23 20:45:13] y方向算符进度: 43/64, 当前算符: D_(0, 3) * D_(40, 43), 耗时: 6.016s
[2025-07-23 20:45:19] y方向算符进度: 44/64, 当前算符: D_(0, 3) * D_(41, 42), 耗时: 6.019s
[2025-07-23 20:45:25] y方向算符进度: 45/64, 当前算符: D_(0, 3) * D_(42, 45), 耗时: 5.980s
[2025-07-23 20:45:31] y方向算符进度: 46/64, 当前算符: D_(0, 3) * D_(43, 44), 耗时: 5.980s
[2025-07-23 20:45:37] y方向算符进度: 47/64, 当前算符: D_(0, 3) * D_(44, 47), 耗时: 6.018s
[2025-07-23 20:45:43] y方向算符进度: 48/64, 当前算符: D_(0, 3) * D_(45, 46), 耗时: 6.020s
[2025-07-23 20:45:49] y方向算符进度: 49/64, 当前算符: D_(0, 3) * D_(48, 51), 耗时: 6.019s
[2025-07-23 20:45:55] y方向算符进度: 50/64, 当前算符: D_(0, 3) * D_(48, 63), 耗时: 5.980s
[2025-07-23 20:46:01] y方向算符进度: 51/64, 当前算符: D_(0, 3) * D_(49, 50), 耗时: 5.980s
[2025-07-23 20:46:07] y方向算符进度: 52/64, 当前算符: D_(0, 3) * D_(49, 62), 耗时: 5.980s
[2025-07-23 20:46:13] y方向算符进度: 53/64, 当前算符: D_(0, 3) * D_(50, 53), 耗时: 6.019s
[2025-07-23 20:46:19] y方向算符进度: 54/64, 当前算符: D_(0, 3) * D_(51, 52), 耗时: 6.016s
[2025-07-23 20:46:25] y方向算符进度: 55/64, 当前算符: D_(0, 3) * D_(52, 55), 耗时: 5.981s
[2025-07-23 20:46:31] y方向算符进度: 56/64, 当前算符: D_(0, 3) * D_(53, 54), 耗时: 5.980s
[2025-07-23 20:46:37] y方向算符进度: 57/64, 当前算符: D_(0, 3) * D_(54, 57), 耗时: 5.980s
[2025-07-23 20:46:43] y方向算符进度: 58/64, 当前算符: D_(0, 3) * D_(55, 56), 耗时: 6.020s
[2025-07-23 20:46:49] y方向算符进度: 59/64, 当前算符: D_(0, 3) * D_(56, 59), 耗时: 6.017s
[2025-07-23 20:46:55] y方向算符进度: 60/64, 当前算符: D_(0, 3) * D_(57, 58), 耗时: 6.019s
[2025-07-23 20:47:01] y方向算符进度: 61/64, 当前算符: D_(0, 3) * D_(58, 61), 耗时: 5.980s
[2025-07-23 20:47:07] y方向算符进度: 62/64, 当前算符: D_(0, 3) * D_(59, 60), 耗时: 5.979s
[2025-07-23 20:47:13] y方向算符进度: 63/64, 当前算符: D_(0, 3) * D_(60, 63), 耗时: 6.017s
[2025-07-23 20:47:19] y方向算符进度: 64/64, 当前算符: D_(0, 3) * D_(61, 62), 耗时: 6.018s
[2025-07-23 20:47:19] y方向二聚体相关函数计算完成,耗时: 379.15 秒
[2025-07-23 20:47:19] 计算傅里叶变换...
[2025-07-23 20:47:19] 二聚体结构因子计算完成
[2025-07-23 20:47:20] 二聚体相关函数平均误差: 0.000478
[2025-07-23 20:47:20] 恢复原始样本数: 4096
[2025-07-23 20:47:20] ================================================================================
[2025-07-23 20:47:20] 所有分析完成
