[2025-07-24 22:22:23] ================================================================================
[2025-07-24 22:22:23] 加载量子态: L=4, J2=1.00, J1=0.74
[2025-07-24 22:22:23] 设置样本数为: 1048576
[2025-07-24 22:22:23] 开始生成共享样本集...
[2025-07-24 22:24:57] 样本生成完成,耗时: 153.627 秒
[2025-07-24 22:24:57] ================================================================================
[2025-07-24 22:24:57] 开始计算简盘结构因子...
[2025-07-24 22:24:57] 识别所有可能的4节点简盘...
[2025-07-24 22:24:57] 找到 64 个有效的4节点简盘
[2025-07-24 22:24:57] 预计算简盘操作符 P_k 和 P_k^-1...
[2025-07-24 22:25:05] 准备简盘相关函数计算...
[2025-07-24 22:25:37] 开始GPU并行计算简盘相关函数 (64 个算符)...
[2025-07-24 22:25:37] 准备GPU并行计算 64 个算符的期望值...
[2025-07-24 22:25:37] 使用并行分块大小: 8
[2025-07-24 22:25:37] 开始生成共享样本集...
[2025-07-24 22:28:09] 样本生成完成,耗时: 152.056 秒
[2025-07-24 22:28:09] GPU并行处理分块 1/8
[2025-07-24 22:28:09]   分块算符 1/8 (全局 1/64): 准备GPU并行计算
[2025-07-24 22:28:09]   分块算符 2/8 (全局 2/64): 准备GPU并行计算
[2025-07-24 22:28:09]   分块算符 3/8 (全局 3/64): 准备GPU并行计算
[2025-07-24 22:28:09]   分块算符 4/8 (全局 4/64): 准备GPU并行计算
[2025-07-24 22:28:09]   分块算符 5/8 (全局 5/64): 准备GPU并行计算
[2025-07-24 22:28:09]   分块算符 6/8 (全局 6/64): 准备GPU并行计算
[2025-07-24 22:28:09]   分块算符 7/8 (全局 7/64): 准备GPU并行计算
[2025-07-24 22:28:09]   分块算符 8/8 (全局 8/64): 准备GPU并行计算
[2025-07-24 22:29:09]     算符 1 计算完成，耗时: 59.791s，结果: 3.372852+0.000122j
