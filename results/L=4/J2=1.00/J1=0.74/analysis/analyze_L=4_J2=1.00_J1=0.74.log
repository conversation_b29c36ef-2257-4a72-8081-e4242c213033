[2025-07-24 23:36:50] ================================================================================
[2025-07-24 23:36:50] 加载量子态: L=4, J2=1.00, J1=0.74
[2025-07-24 23:36:50] 设置样本数为: 1048576
[2025-07-24 23:36:50] 开始生成共享样本集...
[2025-07-24 23:39:22] 样本生成完成,耗时: 152.598 秒
[2025-07-24 23:39:22] ================================================================================
[2025-07-24 23:39:22] 开始计算简盘结构因子...
[2025-07-24 23:39:22] 识别所有可能的4节点简盘...
[2025-07-24 23:39:22] 找到 64 个有效的4节点简盘
[2025-07-24 23:39:22] 预计算简盘操作符 P_k 和 P_k^-1...
[2025-07-24 23:39:30] 准备简盘相关函数计算...
[2025-07-24 23:39:59] 开始netket.jax.expect并行计算简盘相关函数 (64 个算符)...
[2025-07-24 23:39:59] 准备使用netket.jax.expect并行计算 64 个算符的期望值...
[2025-07-24 23:39:59] 使用并行分块大小: 8
[2025-07-24 23:39:59] 使用已存在的共享样本集，样本数: 4096
[2025-07-24 23:39:59] 开始使用netket.jax.expect真正并行计算...
[2025-07-24 23:39:59] netket.jax.expect并行处理分块 1/8
[2025-07-24 23:39:59]   分块算符 1/8 (全局 1/64): 加入并行批次
[2025-07-24 23:39:59]   分块算符 2/8 (全局 2/64): 加入并行批次
[2025-07-24 23:39:59]   分块算符 3/8 (全局 3/64): 加入并行批次
[2025-07-24 23:39:59]   分块算符 4/8 (全局 4/64): 加入并行批次
[2025-07-24 23:39:59]   分块算符 5/8 (全局 5/64): 加入并行批次
[2025-07-24 23:39:59]   分块算符 6/8 (全局 6/64): 加入并行批次
[2025-07-24 23:39:59]   分块算符 7/8 (全局 7/64): 加入并行批次
[2025-07-24 23:39:59]   分块算符 8/8 (全局 8/64): 加入并行批次
[2025-07-24 23:39:59]   开始netket.jax.expect并行计算 8 个算符...
[2025-07-24 23:40:02]   netket.jax.expect计算失败，回退到串行计算: Initializer expected to generate shape (4, 1, 64) but got shape (4, 256, 64) instead for parameter "kernel" in "/dense_symm". (https://flax.readthedocs.io/en/latest/api_reference/flax.errors.html#flax.errors.ScopeParamShapeError)
[2025-07-24 23:40:59]     算符 1 串行计算完成，耗时: 56.402s，结果: 3.373397+0.000029j
