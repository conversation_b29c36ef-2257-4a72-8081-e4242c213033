[2025-07-24 05:40:01] ================================================================================
[2025-07-24 05:40:01] 加载量子态: L=5, J2=0.00, J1=0.10
[2025-07-24 05:40:01] 设置样本数为: 1048576
[2025-07-24 05:40:01] 开始生成共享样本集...
[2025-07-24 05:43:21] 样本生成完成,耗时: 199.504 秒
[2025-07-24 05:43:21] ================================================================================
[2025-07-24 05:43:21] 开始计算自旋结构因子...
[2025-07-24 05:43:21] 初始化操作符缓存...
[2025-07-24 05:43:21] 预构建所有自旋相关操作符...
[2025-07-24 05:43:21] 开始计算自旋相关函数...
[2025-07-24 05:43:32] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 10.582s
[2025-07-24 05:43:44] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 12.179s
[2025-07-24 05:43:50] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.305s
[2025-07-24 05:43:57] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.305s
[2025-07-24 05:44:03] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.272s
[2025-07-24 05:44:09] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.305s
[2025-07-24 05:44:15] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.305s
[2025-07-24 05:44:22] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.272s
[2025-07-24 05:44:28] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.305s
[2025-07-24 05:44:34] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.273s
[2025-07-24 05:44:41] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.303s
[2025-07-24 05:44:47] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.273s
[2025-07-24 05:44:53] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.305s
[2025-07-24 05:44:59] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.306s
[2025-07-24 05:45:06] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.306s
[2025-07-24 05:45:12] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.272s
[2025-07-24 05:45:18] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.307s
[2025-07-24 05:45:25] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.275s
[2025-07-24 05:45:31] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.307s
[2025-07-24 05:45:37] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.307s
[2025-07-24 05:45:44] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.275s
[2025-07-24 05:45:50] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.279s
[2025-07-24 05:45:56] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.307s
[2025-07-24 05:46:02] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.306s
[2025-07-24 05:46:09] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.307s
[2025-07-24 05:46:15] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.311s
[2025-07-24 05:46:21] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.278s
[2025-07-24 05:46:28] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.308s
[2025-07-24 05:46:34] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.276s
[2025-07-24 05:46:40] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.303s
[2025-07-24 05:46:47] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.309s
[2025-07-24 05:46:53] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.307s
[2025-07-24 05:46:59] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.272s
[2025-07-24 05:47:05] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.273s
[2025-07-24 05:47:12] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.305s
[2025-07-24 05:47:18] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.277s
[2025-07-24 05:47:24] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.313s
[2025-07-24 05:47:31] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.312s
[2025-07-24 05:47:37] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.306s
[2025-07-24 05:47:43] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.314s
[2025-07-24 05:47:50] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.306s
[2025-07-24 05:47:56] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.306s
[2025-07-24 05:48:02] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.384s
[2025-07-24 05:48:09] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.307s
[2025-07-24 05:48:15] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.306s
[2025-07-24 05:48:21] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.307s
[2025-07-24 05:48:27] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.303s
[2025-07-24 05:48:34] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.305s
[2025-07-24 05:48:40] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.306s
[2025-07-24 05:48:46] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.272s
[2025-07-24 05:48:53] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.306s
[2025-07-24 05:48:59] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.273s
[2025-07-24 05:49:05] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.306s
[2025-07-24 05:49:12] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.302s
[2025-07-24 05:49:18] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.305s
[2025-07-24 05:49:24] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.274s
[2025-07-24 05:49:30] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.304s
[2025-07-24 05:49:37] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.306s
[2025-07-24 05:49:43] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.304s
[2025-07-24 05:49:49] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.305s
[2025-07-24 05:49:56] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.275s
[2025-07-24 05:50:02] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.272s
[2025-07-24 05:50:08] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.307s
[2025-07-24 05:50:15] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.305s
[2025-07-24 05:50:21] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.312s
[2025-07-24 05:50:27] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.308s
[2025-07-24 05:50:33] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.272s
[2025-07-24 05:50:40] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.305s
[2025-07-24 05:50:46] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.305s
[2025-07-24 05:50:52] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.305s
[2025-07-24 05:50:59] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.305s
[2025-07-24 05:51:05] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.272s
[2025-07-24 05:51:11] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.304s
[2025-07-24 05:51:18] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.304s
[2025-07-24 05:51:24] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.304s
[2025-07-24 05:51:30] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.306s
[2025-07-24 05:51:36] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.275s
[2025-07-24 05:51:43] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.272s
[2025-07-24 05:51:49] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.306s
[2025-07-24 05:51:55] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.305s
[2025-07-24 05:52:02] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.305s
[2025-07-24 05:52:08] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.396s
[2025-07-24 05:52:14] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.274s
[2025-07-24 05:52:21] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.305s
[2025-07-24 05:52:27] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.305s
[2025-07-24 05:52:33] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.306s
[2025-07-24 05:52:39] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.305s
[2025-07-24 05:52:46] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.306s
[2025-07-24 05:52:52] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.272s
[2025-07-24 05:52:58] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.306s
[2025-07-24 05:53:05] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.306s
[2025-07-24 05:53:11] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.306s
[2025-07-24 05:53:17] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.306s
[2025-07-24 05:53:24] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.305s
[2025-07-24 05:53:30] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.272s
[2025-07-24 05:53:36] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.305s
[2025-07-24 05:53:42] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.306s
[2025-07-24 05:53:49] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.305s
[2025-07-24 05:53:55] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.303s
[2025-07-24 05:54:01] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.273s
[2025-07-24 05:54:01] 自旋相关函数计算完成,总耗时 640.20 秒
[2025-07-24 05:54:02] 计算傅里叶变换...
[2025-07-24 05:54:02] 自旋结构因子计算完成
[2025-07-24 05:54:03] 自旋相关函数平均误差: 0.000743
[2025-07-24 05:54:03] ================================================================================
[2025-07-24 05:54:03] 开始计算二聚体结构因子...
[2025-07-24 05:54:03] 识别x和y方向的二聚体...
[2025-07-24 05:54:03] 找到 100 个x方向二聚体和 100 个y方向二聚体
[2025-07-24 05:54:03] 预计算二聚体操作符...
[2025-07-24 05:54:04] 使用x-二聚体 (0, 1) (全局索引 0) 作为参考点, 计算x方向连通二聚体相关函数...
[2025-07-24 05:54:11] x方向算符进度: 1/100, 当前算符: D_(0, 1) * D_(0, 1), 耗时: 6.341s
[2025-07-24 05:54:30] x方向算符进度: 2/100, 当前算符: D_(0, 1) * D_(0, 81), 耗时: 19.003s
[2025-07-24 05:54:38] x方向算符进度: 3/100, 当前算符: D_(0, 1) * D_(1, 20), 耗时: 8.828s
[2025-07-24 05:54:55] x方向算符进度: 4/100, 当前算符: D_(0, 1) * D_(2, 3), 耗时: 16.330s
[2025-07-24 05:55:05] x方向算符进度: 5/100, 当前算符: D_(0, 1) * D_(2, 23), 耗时: 10.536s
[2025-07-24 05:55:16] x方向算符进度: 6/100, 当前算符: D_(0, 1) * D_(3, 82), 耗时: 10.597s
[2025-07-24 05:55:26] x方向算符进度: 7/100, 当前算符: D_(0, 1) * D_(4, 5), 耗时: 10.537s
[2025-07-24 05:55:37] x方向算符进度: 8/100, 当前算符: D_(0, 1) * D_(4, 85), 耗时: 10.599s
[2025-07-24 05:55:48] x方向算符进度: 9/100, 当前算符: D_(0, 1) * D_(5, 24), 耗时: 10.598s
[2025-07-24 05:55:58] x方向算符进度: 10/100, 当前算符: D_(0, 1) * D_(6, 7), 耗时: 10.534s
[2025-07-24 05:56:09] x方向算符进度: 11/100, 当前算符: D_(0, 1) * D_(6, 27), 耗时: 10.595s
[2025-07-24 05:56:19] x方向算符进度: 12/100, 当前算符: D_(0, 1) * D_(7, 86), 耗时: 10.599s
[2025-07-24 05:56:30] x方向算符进度: 13/100, 当前算符: D_(0, 1) * D_(8, 9), 耗时: 10.584s
[2025-07-24 05:56:41] x方向算符进度: 14/100, 当前算符: D_(0, 1) * D_(8, 89), 耗时: 10.595s
[2025-07-24 05:56:51] x方向算符进度: 15/100, 当前算符: D_(0, 1) * D_(9, 28), 耗时: 10.534s
[2025-07-24 05:57:02] x方向算符进度: 16/100, 当前算符: D_(0, 1) * D_(10, 11), 耗时: 10.609s
[2025-07-24 05:57:12] x方向算符进度: 17/100, 当前算符: D_(0, 1) * D_(10, 31), 耗时: 10.534s
[2025-07-24 05:57:23] x方向算符进度: 18/100, 当前算符: D_(0, 1) * D_(11, 90), 耗时: 10.578s
[2025-07-24 05:57:33] x方向算符进度: 19/100, 当前算符: D_(0, 1) * D_(12, 13), 耗时: 10.595s
[2025-07-24 05:57:44] x方向算符进度: 20/100, 当前算符: D_(0, 1) * D_(12, 93), 耗时: 10.578s
[2025-07-24 05:57:55] x方向算符进度: 21/100, 当前算符: D_(0, 1) * D_(13, 32), 耗时: 10.594s
[2025-07-24 05:58:05] x方向算符进度: 22/100, 当前算符: D_(0, 1) * D_(14, 15), 耗时: 10.533s
[2025-07-24 05:58:16] x方向算符进度: 23/100, 当前算符: D_(0, 1) * D_(14, 35), 耗时: 10.598s
[2025-07-24 05:58:26] x方向算符进度: 24/100, 当前算符: D_(0, 1) * D_(15, 94), 耗时: 10.584s
[2025-07-24 05:58:37] x方向算符进度: 25/100, 当前算符: D_(0, 1) * D_(16, 17), 耗时: 10.599s
[2025-07-24 05:58:48] x方向算符进度: 26/100, 当前算符: D_(0, 1) * D_(16, 97), 耗时: 10.596s
[2025-07-24 05:58:58] x方向算符进度: 27/100, 当前算符: D_(0, 1) * D_(17, 36), 耗时: 10.537s
[2025-07-24 05:59:09] x方向算符进度: 28/100, 当前算符: D_(0, 1) * D_(18, 19), 耗时: 10.594s
[2025-07-24 05:59:19] x方向算符进度: 29/100, 当前算符: D_(0, 1) * D_(18, 39), 耗时: 10.578s
[2025-07-24 05:59:30] x方向算符进度: 30/100, 当前算符: D_(0, 1) * D_(19, 98), 耗时: 10.599s
[2025-07-24 05:59:40] x方向算符进度: 31/100, 当前算符: D_(0, 1) * D_(20, 21), 耗时: 10.598s
[2025-07-24 05:59:51] x方向算符进度: 32/100, 当前算符: D_(0, 1) * D_(21, 40), 耗时: 10.536s
[2025-07-24 06:00:02] x方向算符进度: 33/100, 当前算符: D_(0, 1) * D_(22, 23), 耗时: 10.597s
[2025-07-24 06:00:12] x方向算符进度: 34/100, 当前算符: D_(0, 1) * D_(22, 43), 耗时: 10.534s
[2025-07-24 06:00:23] x方向算符进度: 35/100, 当前算符: D_(0, 1) * D_(24, 25), 耗时: 10.594s
[2025-07-24 06:00:33] x方向算符进度: 36/100, 当前算符: D_(0, 1) * D_(25, 44), 耗时: 10.579s
[2025-07-24 06:00:44] x方向算符进度: 37/100, 当前算符: D_(0, 1) * D_(26, 27), 耗时: 10.594s
[2025-07-24 06:00:54] x方向算符进度: 38/100, 当前算符: D_(0, 1) * D_(26, 47), 耗时: 10.535s
[2025-07-24 06:01:05] x方向算符进度: 39/100, 当前算符: D_(0, 1) * D_(28, 29), 耗时: 10.596s
[2025-07-24 06:01:16] x方向算符进度: 40/100, 当前算符: D_(0, 1) * D_(29, 48), 耗时: 10.580s
[2025-07-24 06:01:26] x方向算符进度: 41/100, 当前算符: D_(0, 1) * D_(30, 31), 耗时: 10.595s
[2025-07-24 06:01:37] x方向算符进度: 42/100, 当前算符: D_(0, 1) * D_(30, 51), 耗时: 10.583s
[2025-07-24 06:01:47] x方向算符进度: 43/100, 当前算符: D_(0, 1) * D_(32, 33), 耗时: 10.580s
[2025-07-24 06:01:58] x方向算符进度: 44/100, 当前算符: D_(0, 1) * D_(33, 52), 耗时: 10.596s
[2025-07-24 06:02:08] x方向算符进度: 45/100, 当前算符: D_(0, 1) * D_(34, 35), 耗时: 10.533s
[2025-07-24 06:02:19] x方向算符进度: 46/100, 当前算符: D_(0, 1) * D_(34, 55), 耗时: 10.593s
[2025-07-24 06:02:30] x方向算符进度: 47/100, 当前算符: D_(0, 1) * D_(36, 37), 耗时: 10.593s
[2025-07-24 06:02:40] x方向算符进度: 48/100, 当前算符: D_(0, 1) * D_(37, 56), 耗时: 10.577s
[2025-07-24 06:02:51] x方向算符进度: 49/100, 当前算符: D_(0, 1) * D_(38, 39), 耗时: 10.595s
[2025-07-24 06:03:01] x方向算符进度: 50/100, 当前算符: D_(0, 1) * D_(38, 59), 耗时: 10.535s
[2025-07-24 06:03:12] x方向算符进度: 51/100, 当前算符: D_(0, 1) * D_(40, 41), 耗时: 10.594s
[2025-07-24 06:03:23] x方向算符进度: 52/100, 当前算符: D_(0, 1) * D_(41, 60), 耗时: 10.581s
[2025-07-24 06:03:33] x方向算符进度: 53/100, 当前算符: D_(0, 1) * D_(42, 43), 耗时: 10.596s
[2025-07-24 06:03:44] x方向算符进度: 54/100, 当前算符: D_(0, 1) * D_(42, 63), 耗时: 10.576s
[2025-07-24 06:03:54] x方向算符进度: 55/100, 当前算符: D_(0, 1) * D_(44, 45), 耗时: 10.599s
[2025-07-24 06:04:05] x方向算符进度: 56/100, 当前算符: D_(0, 1) * D_(45, 64), 耗时: 10.535s
[2025-07-24 06:04:15] x方向算符进度: 57/100, 当前算符: D_(0, 1) * D_(46, 47), 耗时: 10.595s
[2025-07-24 06:04:26] x方向算符进度: 58/100, 当前算符: D_(0, 1) * D_(46, 67), 耗时: 10.581s
[2025-07-24 06:04:37] x方向算符进度: 59/100, 当前算符: D_(0, 1) * D_(48, 49), 耗时: 10.594s
[2025-07-24 06:04:47] x方向算符进度: 60/100, 当前算符: D_(0, 1) * D_(49, 68), 耗时: 10.533s
[2025-07-24 06:04:58] x方向算符进度: 61/100, 当前算符: D_(0, 1) * D_(50, 51), 耗时: 10.596s
[2025-07-24 06:05:08] x方向算符进度: 62/100, 当前算符: D_(0, 1) * D_(50, 71), 耗时: 10.585s
[2025-07-24 06:05:19] x方向算符进度: 63/100, 当前算符: D_(0, 1) * D_(52, 53), 耗时: 10.584s
[2025-07-24 06:05:30] x方向算符进度: 64/100, 当前算符: D_(0, 1) * D_(53, 72), 耗时: 10.594s
[2025-07-24 06:05:40] x方向算符进度: 65/100, 当前算符: D_(0, 1) * D_(54, 55), 耗时: 10.533s
[2025-07-24 06:05:51] x方向算符进度: 66/100, 当前算符: D_(0, 1) * D_(54, 75), 耗时: 10.594s
[2025-07-24 06:06:01] x方向算符进度: 67/100, 当前算符: D_(0, 1) * D_(56, 57), 耗时: 10.535s
[2025-07-24 06:06:12] x方向算符进度: 68/100, 当前算符: D_(0, 1) * D_(57, 76), 耗时: 10.598s
[2025-07-24 06:06:22] x方向算符进度: 69/100, 当前算符: D_(0, 1) * D_(58, 59), 耗时: 10.594s
[2025-07-24 06:06:33] x方向算符进度: 70/100, 当前算符: D_(0, 1) * D_(58, 79), 耗时: 10.534s
[2025-07-24 06:06:44] x方向算符进度: 71/100, 当前算符: D_(0, 1) * D_(60, 61), 耗时: 10.593s
[2025-07-24 06:06:54] x方向算符进度: 72/100, 当前算符: D_(0, 1) * D_(61, 80), 耗时: 10.534s
[2025-07-24 06:07:05] x方向算符进度: 73/100, 当前算符: D_(0, 1) * D_(62, 63), 耗时: 10.594s
[2025-07-24 06:07:15] x方向算符进度: 74/100, 当前算符: D_(0, 1) * D_(62, 83), 耗时: 10.579s
[2025-07-24 06:07:26] x方向算符进度: 75/100, 当前算符: D_(0, 1) * D_(64, 65), 耗时: 10.594s
[2025-07-24 06:07:36] x方向算符进度: 76/100, 当前算符: D_(0, 1) * D_(65, 84), 耗时: 10.533s
[2025-07-24 06:07:47] x方向算符进度: 77/100, 当前算符: D_(0, 1) * D_(66, 67), 耗时: 10.594s
[2025-07-24 06:07:57] x方向算符进度: 78/100, 当前算符: D_(0, 1) * D_(66, 87), 耗时: 10.535s
[2025-07-24 06:08:08] x方向算符进度: 79/100, 当前算符: D_(0, 1) * D_(68, 69), 耗时: 10.536s
[2025-07-24 06:08:19] x方向算符进度: 80/100, 当前算符: D_(0, 1) * D_(69, 88), 耗时: 10.595s
[2025-07-24 06:08:29] x方向算符进度: 81/100, 当前算符: D_(0, 1) * D_(70, 71), 耗时: 10.584s
[2025-07-24 06:08:40] x方向算符进度: 82/100, 当前算符: D_(0, 1) * D_(70, 91), 耗时: 10.596s
[2025-07-24 06:08:50] x方向算符进度: 83/100, 当前算符: D_(0, 1) * D_(72, 73), 耗时: 10.534s
[2025-07-24 06:09:01] x方向算符进度: 84/100, 当前算符: D_(0, 1) * D_(73, 92), 耗时: 10.594s
[2025-07-24 06:09:12] x方向算符进度: 85/100, 当前算符: D_(0, 1) * D_(74, 75), 耗时: 10.584s
[2025-07-24 06:09:22] x方向算符进度: 86/100, 当前算符: D_(0, 1) * D_(74, 95), 耗时: 10.581s
[2025-07-24 06:09:33] x方向算符进度: 87/100, 当前算符: D_(0, 1) * D_(76, 77), 耗时: 10.598s
[2025-07-24 06:09:43] x方向算符进度: 88/100, 当前算符: D_(0, 1) * D_(77, 96), 耗时: 10.535s
[2025-07-24 06:09:54] x方向算符进度: 89/100, 当前算符: D_(0, 1) * D_(78, 79), 耗时: 10.597s
[2025-07-24 06:10:04] x方向算符进度: 90/100, 当前算符: D_(0, 1) * D_(78, 99), 耗时: 10.534s
[2025-07-24 06:10:15] x方向算符进度: 91/100, 当前算符: D_(0, 1) * D_(80, 81), 耗时: 10.598s
[2025-07-24 06:10:26] x方向算符进度: 92/100, 当前算符: D_(0, 1) * D_(82, 83), 耗时: 10.583s
[2025-07-24 06:10:36] x方向算符进度: 93/100, 当前算符: D_(0, 1) * D_(84, 85), 耗时: 10.595s
[2025-07-24 06:10:47] x方向算符进度: 94/100, 当前算符: D_(0, 1) * D_(86, 87), 耗时: 10.577s
[2025-07-24 06:10:57] x方向算符进度: 95/100, 当前算符: D_(0, 1) * D_(88, 89), 耗时: 10.534s
[2025-07-24 06:11:08] x方向算符进度: 96/100, 当前算符: D_(0, 1) * D_(90, 91), 耗时: 10.597s
[2025-07-24 06:11:18] x方向算符进度: 97/100, 当前算符: D_(0, 1) * D_(92, 93), 耗时: 10.535s
[2025-07-24 06:11:29] x方向算符进度: 98/100, 当前算符: D_(0, 1) * D_(94, 95), 耗时: 10.595s
[2025-07-24 06:11:40] x方向算符进度: 99/100, 当前算符: D_(0, 1) * D_(96, 97), 耗时: 10.584s
[2025-07-24 06:11:50] x方向算符进度: 100/100, 当前算符: D_(0, 1) * D_(98, 99), 耗时: 10.599s
[2025-07-24 06:11:50] x方向二聚体相关函数计算完成,耗时: 1065.90 秒
[2025-07-24 06:11:50] --------------------------------------------------------------------------------
[2025-07-24 06:11:50] 使用y-二聚体 (0, 3) (全局索引 100) 作为参考点, 计算y方向连通二聚体相关函数...
[2025-07-24 06:11:56] y方向算符进度: 1/100, 当前算符: D_(0, 3) * D_(0, 3), 耗时: 6.282s
[2025-07-24 06:12:05] y方向算符进度: 2/100, 当前算符: D_(0, 3) * D_(0, 19), 耗时: 8.815s
[2025-07-24 06:12:16] y方向算符进度: 3/100, 当前算符: D_(0, 3) * D_(1, 2), 耗时: 10.624s
[2025-07-24 06:12:26] y方向算符进度: 4/100, 当前算符: D_(0, 3) * D_(1, 18), 耗时: 10.560s
[2025-07-24 06:12:37] y方向算符进度: 5/100, 当前算符: D_(0, 3) * D_(2, 5), 耗时: 10.559s
[2025-07-24 06:12:46] y方向算符进度: 6/100, 当前算符: D_(0, 3) * D_(3, 4), 耗时: 8.811s
[2025-07-24 06:12:56] y方向算符进度: 7/100, 当前算符: D_(0, 3) * D_(4, 7), 耗时: 10.577s
[2025-07-24 06:13:07] y方向算符进度: 8/100, 当前算符: D_(0, 3) * D_(5, 6), 耗时: 10.627s
[2025-07-24 06:13:18] y方向算符进度: 9/100, 当前算符: D_(0, 3) * D_(6, 9), 耗时: 10.558s
[2025-07-24 06:13:28] y方向算符进度: 10/100, 当前算符: D_(0, 3) * D_(7, 8), 耗时: 10.628s
[2025-07-24 06:13:39] y方向算符进度: 11/100, 当前算符: D_(0, 3) * D_(8, 11), 耗时: 10.555s
[2025-07-24 06:13:49] y方向算符进度: 12/100, 当前算符: D_(0, 3) * D_(9, 10), 耗时: 10.625s
[2025-07-24 06:14:00] y方向算符进度: 13/100, 当前算符: D_(0, 3) * D_(10, 13), 耗时: 10.556s
[2025-07-24 06:14:11] y方向算符进度: 14/100, 当前算符: D_(0, 3) * D_(11, 12), 耗时: 10.617s
[2025-07-24 06:14:21] y方向算符进度: 15/100, 当前算符: D_(0, 3) * D_(12, 15), 耗时: 10.585s
[2025-07-24 06:14:32] y方向算符进度: 16/100, 当前算符: D_(0, 3) * D_(13, 14), 耗时: 10.619s
[2025-07-24 06:14:42] y方向算符进度: 17/100, 当前算符: D_(0, 3) * D_(14, 17), 耗时: 10.563s
[2025-07-24 06:14:53] y方向算符进度: 18/100, 当前算符: D_(0, 3) * D_(15, 16), 耗时: 10.556s
[2025-07-24 06:15:04] y方向算符进度: 19/100, 当前算符: D_(0, 3) * D_(16, 19), 耗时: 10.634s
[2025-07-24 06:15:14] y方向算符进度: 20/100, 当前算符: D_(0, 3) * D_(17, 18), 耗时: 10.583s
[2025-07-24 06:15:25] y方向算符进度: 21/100, 当前算符: D_(0, 3) * D_(20, 23), 耗时: 10.624s
[2025-07-24 06:15:35] y方向算符进度: 22/100, 当前算符: D_(0, 3) * D_(20, 39), 耗时: 10.556s
[2025-07-24 06:15:46] y方向算符进度: 23/100, 当前算符: D_(0, 3) * D_(21, 22), 耗时: 10.627s
[2025-07-24 06:15:57] y方向算符进度: 24/100, 当前算符: D_(0, 3) * D_(21, 38), 耗时: 10.579s
[2025-07-24 06:16:07] y方向算符进度: 25/100, 当前算符: D_(0, 3) * D_(22, 25), 耗时: 10.622s
[2025-07-24 06:16:18] y方向算符进度: 26/100, 当前算符: D_(0, 3) * D_(23, 24), 耗时: 10.580s
[2025-07-24 06:16:28] y方向算符进度: 27/100, 当前算符: D_(0, 3) * D_(24, 27), 耗时: 10.631s
[2025-07-24 06:16:39] y方向算符进度: 28/100, 当前算符: D_(0, 3) * D_(25, 26), 耗时: 10.584s
[2025-07-24 06:16:50] y方向算符进度: 29/100, 当前算符: D_(0, 3) * D_(26, 29), 耗时: 10.637s
[2025-07-24 06:17:00] y方向算符进度: 30/100, 当前算符: D_(0, 3) * D_(27, 28), 耗时: 10.619s
[2025-07-24 06:17:11] y方向算符进度: 31/100, 当前算符: D_(0, 3) * D_(28, 31), 耗时: 10.562s
[2025-07-24 06:17:21] y方向算符进度: 32/100, 当前算符: D_(0, 3) * D_(29, 30), 耗时: 10.627s
[2025-07-24 06:17:32] y方向算符进度: 33/100, 当前算符: D_(0, 3) * D_(30, 33), 耗时: 10.586s
[2025-07-24 06:17:43] y方向算符进度: 34/100, 当前算符: D_(0, 3) * D_(31, 32), 耗时: 10.581s
[2025-07-24 06:17:53] y方向算符进度: 35/100, 当前算符: D_(0, 3) * D_(32, 35), 耗时: 10.618s
[2025-07-24 06:18:04] y方向算符进度: 36/100, 当前算符: D_(0, 3) * D_(33, 34), 耗时: 10.554s
[2025-07-24 06:18:14] y方向算符进度: 37/100, 当前算符: D_(0, 3) * D_(34, 37), 耗时: 10.626s
[2025-07-24 06:18:25] y方向算符进度: 38/100, 当前算符: D_(0, 3) * D_(35, 36), 耗时: 10.586s
[2025-07-24 06:18:36] y方向算符进度: 39/100, 当前算符: D_(0, 3) * D_(36, 39), 耗时: 10.632s
[2025-07-24 06:18:46] y方向算符进度: 40/100, 当前算符: D_(0, 3) * D_(37, 38), 耗时: 10.556s
[2025-07-24 06:18:57] y方向算符进度: 41/100, 当前算符: D_(0, 3) * D_(40, 43), 耗时: 10.632s
[2025-07-24 06:19:07] y方向算符进度: 42/100, 当前算符: D_(0, 3) * D_(40, 59), 耗时: 10.560s
[2025-07-24 06:19:18] y方向算符进度: 43/100, 当前算符: D_(0, 3) * D_(41, 42), 耗时: 10.628s
[2025-07-24 06:19:29] y方向算符进度: 44/100, 当前算符: D_(0, 3) * D_(41, 58), 耗时: 10.583s
[2025-07-24 06:19:39] y方向算符进度: 45/100, 当前算符: D_(0, 3) * D_(42, 45), 耗时: 10.620s
[2025-07-24 06:19:50] y方向算符进度: 46/100, 当前算符: D_(0, 3) * D_(43, 44), 耗时: 10.626s
[2025-07-24 06:20:00] y方向算符进度: 47/100, 当前算符: D_(0, 3) * D_(44, 47), 耗时: 10.558s
[2025-07-24 06:20:11] y方向算符进度: 48/100, 当前算符: D_(0, 3) * D_(45, 46), 耗时: 10.630s
[2025-07-24 06:20:22] y方向算符进度: 49/100, 当前算符: D_(0, 3) * D_(46, 49), 耗时: 10.555s
[2025-07-24 06:20:32] y方向算符进度: 50/100, 当前算符: D_(0, 3) * D_(47, 48), 耗时: 10.702s
[2025-07-24 06:20:43] y方向算符进度: 51/100, 当前算符: D_(0, 3) * D_(48, 51), 耗时: 10.627s
[2025-07-24 06:20:53] y方向算符进度: 52/100, 当前算符: D_(0, 3) * D_(49, 50), 耗时: 10.581s
[2025-07-24 06:21:04] y方向算符进度: 53/100, 当前算符: D_(0, 3) * D_(50, 53), 耗时: 10.620s
[2025-07-24 06:21:15] y方向算符进度: 54/100, 当前算符: D_(0, 3) * D_(51, 52), 耗时: 10.560s
[2025-07-24 06:21:25] y方向算符进度: 55/100, 当前算符: D_(0, 3) * D_(52, 55), 耗时: 10.630s
[2025-07-24 06:21:36] y方向算符进度: 56/100, 当前算符: D_(0, 3) * D_(53, 54), 耗时: 10.579s
[2025-07-24 06:21:46] y方向算符进度: 57/100, 当前算符: D_(0, 3) * D_(54, 57), 耗时: 10.624s
[2025-07-24 06:21:57] y方向算符进度: 58/100, 当前算符: D_(0, 3) * D_(55, 56), 耗时: 10.582s
[2025-07-24 06:22:08] y方向算符进度: 59/100, 当前算符: D_(0, 3) * D_(56, 59), 耗时: 10.637s
[2025-07-24 06:22:18] y方向算符进度: 60/100, 当前算符: D_(0, 3) * D_(57, 58), 耗时: 10.561s
[2025-07-24 06:22:29] y方向算符进度: 61/100, 当前算符: D_(0, 3) * D_(60, 63), 耗时: 10.625s
[2025-07-24 06:22:40] y方向算符进度: 62/100, 当前算符: D_(0, 3) * D_(60, 79), 耗时: 10.629s
[2025-07-24 06:22:50] y方向算符进度: 63/100, 当前算符: D_(0, 3) * D_(61, 62), 耗时: 10.585s
[2025-07-24 06:23:01] y方向算符进度: 64/100, 当前算符: D_(0, 3) * D_(61, 78), 耗时: 10.632s
[2025-07-24 06:23:11] y方向算符进度: 65/100, 当前算符: D_(0, 3) * D_(62, 65), 耗时: 10.558s
[2025-07-24 06:23:22] y方向算符进度: 66/100, 当前算符: D_(0, 3) * D_(63, 64), 耗时: 10.616s
[2025-07-24 06:23:32] y方向算符进度: 67/100, 当前算符: D_(0, 3) * D_(64, 67), 耗时: 10.579s
[2025-07-24 06:23:43] y方向算符进度: 68/100, 当前算符: D_(0, 3) * D_(65, 66), 耗时: 10.577s
[2025-07-24 06:23:54] y方向算符进度: 69/100, 当前算符: D_(0, 3) * D_(66, 69), 耗时: 10.622s
[2025-07-24 06:24:04] y方向算符进度: 70/100, 当前算符: D_(0, 3) * D_(67, 68), 耗时: 10.556s
[2025-07-24 06:24:15] y方向算符进度: 71/100, 当前算符: D_(0, 3) * D_(68, 71), 耗时: 10.623s
[2025-07-24 06:24:25] y方向算符进度: 72/100, 当前算符: D_(0, 3) * D_(69, 70), 耗时: 10.578s
[2025-07-24 06:24:36] y方向算符进度: 73/100, 当前算符: D_(0, 3) * D_(70, 73), 耗时: 10.626s
[2025-07-24 06:24:47] y方向算符进度: 74/100, 当前算符: D_(0, 3) * D_(71, 72), 耗时: 10.583s
[2025-07-24 06:24:57] y方向算符进度: 75/100, 当前算符: D_(0, 3) * D_(72, 75), 耗时: 10.619s
[2025-07-24 06:25:08] y方向算符进度: 76/100, 当前算符: D_(0, 3) * D_(73, 74), 耗时: 10.572s
[2025-07-24 06:25:18] y方向算符进度: 77/100, 当前算符: D_(0, 3) * D_(74, 77), 耗时: 10.619s
[2025-07-24 06:25:29] y方向算符进度: 78/100, 当前算符: D_(0, 3) * D_(75, 76), 耗时: 10.618s
[2025-07-24 06:25:40] y方向算符进度: 79/100, 当前算符: D_(0, 3) * D_(76, 79), 耗时: 10.583s
[2025-07-24 06:25:50] y方向算符进度: 80/100, 当前算符: D_(0, 3) * D_(77, 78), 耗时: 10.622s
[2025-07-24 06:26:01] y方向算符进度: 81/100, 当前算符: D_(0, 3) * D_(80, 83), 耗时: 10.559s
[2025-07-24 06:26:11] y方向算符进度: 82/100, 当前算符: D_(0, 3) * D_(80, 99), 耗时: 10.628s
[2025-07-24 06:26:22] y方向算符进度: 83/100, 当前算符: D_(0, 3) * D_(81, 82), 耗时: 10.578s
[2025-07-24 06:26:33] y方向算符进度: 84/100, 当前算符: D_(0, 3) * D_(81, 98), 耗时: 10.625s
[2025-07-24 06:26:43] y方向算符进度: 85/100, 当前算符: D_(0, 3) * D_(82, 85), 耗时: 10.557s
[2025-07-24 06:26:54] y方向算符进度: 86/100, 当前算符: D_(0, 3) * D_(83, 84), 耗时: 10.790s
[2025-07-24 06:27:05] y方向算符进度: 87/100, 当前算符: D_(0, 3) * D_(84, 87), 耗时: 10.557s
[2025-07-24 06:27:15] y方向算符进度: 88/100, 当前算符: D_(0, 3) * D_(85, 86), 耗时: 10.621s
[2025-07-24 06:27:26] y方向算符进度: 89/100, 当前算符: D_(0, 3) * D_(86, 89), 耗时: 10.579s
[2025-07-24 06:27:36] y方向算符进度: 90/100, 当前算符: D_(0, 3) * D_(87, 88), 耗时: 10.637s
[2025-07-24 06:27:47] y方向算符进度: 91/100, 当前算符: D_(0, 3) * D_(88, 91), 耗时: 10.584s
[2025-07-24 06:27:58] y方向算符进度: 92/100, 当前算符: D_(0, 3) * D_(89, 90), 耗时: 10.628s
[2025-07-24 06:28:08] y方向算符进度: 93/100, 当前算符: D_(0, 3) * D_(90, 93), 耗时: 10.560s
[2025-07-24 06:28:19] y方向算符进度: 94/100, 当前算符: D_(0, 3) * D_(91, 92), 耗时: 10.571s
[2025-07-24 06:28:29] y方向算符进度: 95/100, 当前算符: D_(0, 3) * D_(92, 95), 耗时: 10.623s
[2025-07-24 06:28:40] y方向算符进度: 96/100, 当前算符: D_(0, 3) * D_(93, 94), 耗时: 10.576s
[2025-07-24 06:28:51] y方向算符进度: 97/100, 当前算符: D_(0, 3) * D_(94, 97), 耗时: 10.631s
[2025-07-24 06:29:01] y方向算符进度: 98/100, 当前算符: D_(0, 3) * D_(95, 96), 耗时: 10.584s
[2025-07-24 06:29:12] y方向算符进度: 99/100, 当前算符: D_(0, 3) * D_(96, 99), 耗时: 10.626s
[2025-07-24 06:29:22] y方向算符进度: 100/100, 当前算符: D_(0, 3) * D_(97, 98), 耗时: 10.554s
[2025-07-24 06:29:22] y方向二聚体相关函数计算完成,耗时: 1052.22 秒
[2025-07-24 06:29:23] 计算傅里叶变换...
[2025-07-24 06:29:23] 二聚体结构因子计算完成
[2025-07-24 06:29:24] 二聚体相关函数平均误差: 0.000553
[2025-07-24 06:29:24] 恢复原始样本数: 4096
[2025-07-24 06:29:24] ================================================================================
[2025-07-24 06:29:24] 所有分析完成
