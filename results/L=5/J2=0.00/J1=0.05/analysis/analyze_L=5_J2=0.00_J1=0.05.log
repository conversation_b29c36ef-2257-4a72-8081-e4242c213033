[2025-07-24 01:31:54] ================================================================================
[2025-07-24 01:31:54] 加载量子态: L=5, J2=0.00, J1=0.05
[2025-07-24 01:31:54] 设置样本数为: 1048576
[2025-07-24 01:31:54] 开始生成共享样本集...
[2025-07-24 01:35:14] 样本生成完成,耗时: 199.523 秒
[2025-07-24 01:35:14] ================================================================================
[2025-07-24 01:35:14] 开始计算自旋结构因子...
[2025-07-24 01:35:14] 初始化操作符缓存...
[2025-07-24 01:35:14] 预构建所有自旋相关操作符...
[2025-07-24 01:35:14] 开始计算自旋相关函数...
[2025-07-24 01:35:24] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 10.410s
[2025-07-24 01:35:36] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 12.102s
[2025-07-24 01:35:43] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.267s
[2025-07-24 01:35:49] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.288s
[2025-07-24 01:35:55] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.270s
[2025-07-24 01:36:02] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.287s
[2025-07-24 01:36:08] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.288s
[2025-07-24 01:36:14] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.275s
[2025-07-24 01:36:20] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.288s
[2025-07-24 01:36:27] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.273s
[2025-07-24 01:36:33] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.284s
[2025-07-24 01:36:39] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.250s
[2025-07-24 01:36:45] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.288s
[2025-07-24 01:36:52] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.284s
[2025-07-24 01:36:58] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.287s
[2025-07-24 01:37:04] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.273s
[2025-07-24 01:37:11] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.288s
[2025-07-24 01:37:17] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.274s
[2025-07-24 01:37:23] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.278s
[2025-07-24 01:37:29] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.289s
[2025-07-24 01:37:36] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.268s
[2025-07-24 01:37:42] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.279s
[2025-07-24 01:37:48] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.289s
[2025-07-24 01:37:55] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.271s
[2025-07-24 01:38:01] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.288s
[2025-07-24 01:38:07] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.287s
[2025-07-24 01:38:13] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.272s
[2025-07-24 01:38:20] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.289s
[2025-07-24 01:38:26] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.275s
[2025-07-24 01:38:32] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.265s
[2025-07-24 01:38:39] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.291s
[2025-07-24 01:38:45] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.292s
[2025-07-24 01:38:51] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.269s
[2025-07-24 01:38:57] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.310s
[2025-07-24 01:39:04] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.283s
[2025-07-24 01:39:10] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.273s
[2025-07-24 01:39:16] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.288s
[2025-07-24 01:39:23] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.287s
[2025-07-24 01:39:29] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.277s
[2025-07-24 01:39:35] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.289s
[2025-07-24 01:39:41] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.273s
[2025-07-24 01:39:48] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.271s
[2025-07-24 01:39:54] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.284s
[2025-07-24 01:40:00] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.280s
[2025-07-24 01:40:07] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.290s
[2025-07-24 01:40:13] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.289s
[2025-07-24 01:40:19] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.273s
[2025-07-24 01:40:25] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.284s
[2025-07-24 01:40:32] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.312s
[2025-07-24 01:40:38] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.268s
[2025-07-24 01:40:44] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.293s
[2025-07-24 01:40:51] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.276s
[2025-07-24 01:40:57] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.276s
[2025-07-24 01:41:03] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.262s
[2025-07-24 01:41:09] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.290s
[2025-07-24 01:41:16] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.252s
[2025-07-24 01:41:22] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.290s
[2025-07-24 01:41:28] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.292s
[2025-07-24 01:41:34] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.272s
[2025-07-24 01:41:41] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.291s
[2025-07-24 01:41:47] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.278s
[2025-07-24 01:41:53] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.269s
[2025-07-24 01:42:00] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.288s
[2025-07-24 01:42:06] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.277s
[2025-07-24 01:42:12] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.290s
[2025-07-24 01:42:18] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.292s
[2025-07-24 01:42:25] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.275s
[2025-07-24 01:42:31] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.291s
[2025-07-24 01:42:37] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.286s
[2025-07-24 01:42:44] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.271s
[2025-07-24 01:42:50] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.288s
[2025-07-24 01:42:56] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.270s
[2025-07-24 01:43:02] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.287s
[2025-07-24 01:43:09] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.287s
[2025-07-24 01:43:15] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.271s
[2025-07-24 01:43:21] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.288s
[2025-07-24 01:43:28] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.277s
[2025-07-24 01:43:34] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.265s
[2025-07-24 01:43:40] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.287s
[2025-07-24 01:43:46] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.272s
[2025-07-24 01:43:53] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.287s
[2025-07-24 01:43:59] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.285s
[2025-07-24 01:44:05] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.251s
[2025-07-24 01:44:12] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.289s
[2025-07-24 01:44:18] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.289s
[2025-07-24 01:44:24] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.291s
[2025-07-24 01:44:30] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.273s
[2025-07-24 01:44:37] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.289s
[2025-07-24 01:44:43] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.268s
[2025-07-24 01:44:49] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.271s
[2025-07-24 01:44:55] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.295s
[2025-07-24 01:45:02] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.278s
[2025-07-24 01:45:08] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.289s
[2025-07-24 01:45:14] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.288s
[2025-07-24 01:45:21] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.272s
[2025-07-24 01:45:27] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.287s
[2025-07-24 01:45:33] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.274s
[2025-07-24 01:45:39] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.268s
[2025-07-24 01:45:46] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.286s
[2025-07-24 01:45:52] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.267s
[2025-07-24 01:45:52] 自旋相关函数计算完成,总耗时 638.11 秒
[2025-07-24 01:45:52] 计算傅里叶变换...
[2025-07-24 01:45:53] 自旋结构因子计算完成
[2025-07-24 01:45:54] 自旋相关函数平均误差: 0.000743
[2025-07-24 01:45:54] ================================================================================
[2025-07-24 01:45:54] 开始计算二聚体结构因子...
[2025-07-24 01:45:54] 识别x和y方向的二聚体...
[2025-07-24 01:45:54] 找到 100 个x方向二聚体和 100 个y方向二聚体
[2025-07-24 01:45:54] 预计算二聚体操作符...
[2025-07-24 01:45:55] 使用x-二聚体 (0, 1) (全局索引 0) 作为参考点, 计算x方向连通二聚体相关函数...
[2025-07-24 01:46:01] x方向算符进度: 1/100, 当前算符: D_(0, 1) * D_(0, 1), 耗时: 6.336s
[2025-07-24 01:46:20] x方向算符进度: 2/100, 当前算符: D_(0, 1) * D_(0, 81), 耗时: 18.972s
[2025-07-24 01:46:29] x方向算符进度: 3/100, 当前算符: D_(0, 1) * D_(1, 20), 耗时: 8.779s
[2025-07-24 01:46:45] x方向算符进度: 4/100, 当前算符: D_(0, 1) * D_(2, 3), 耗时: 16.292s
[2025-07-24 01:46:56] x方向算符进度: 5/100, 当前算符: D_(0, 1) * D_(2, 23), 耗时: 10.509s
[2025-07-24 01:47:06] x方向算符进度: 6/100, 当前算符: D_(0, 1) * D_(3, 82), 耗时: 10.562s
[2025-07-24 01:47:17] x方向算符进度: 7/100, 当前算符: D_(0, 1) * D_(4, 5), 耗时: 10.511s
[2025-07-24 01:47:27] x方向算符进度: 8/100, 当前算符: D_(0, 1) * D_(4, 85), 耗时: 10.592s
[2025-07-24 01:47:38] x方向算符进度: 9/100, 当前算符: D_(0, 1) * D_(5, 24), 耗时: 10.563s
[2025-07-24 01:47:49] x方向算符进度: 10/100, 当前算符: D_(0, 1) * D_(6, 7), 耗时: 10.522s
[2025-07-24 01:47:59] x方向算符进度: 11/100, 当前算符: D_(0, 1) * D_(6, 27), 耗时: 10.554s
[2025-07-24 01:48:10] x方向算符进度: 12/100, 当前算符: D_(0, 1) * D_(7, 86), 耗时: 10.568s
[2025-07-24 01:48:20] x方向算符进度: 13/100, 当前算符: D_(0, 1) * D_(8, 9), 耗时: 10.502s
[2025-07-24 01:48:31] x方向算符进度: 14/100, 当前算符: D_(0, 1) * D_(8, 89), 耗时: 10.565s
[2025-07-24 01:48:41] x方向算符进度: 15/100, 当前算符: D_(0, 1) * D_(9, 28), 耗时: 10.524s
[2025-07-24 01:48:52] x方向算符进度: 16/100, 当前算符: D_(0, 1) * D_(10, 11), 耗时: 10.562s
[2025-07-24 01:49:02] x方向算符进度: 17/100, 当前算符: D_(0, 1) * D_(10, 31), 耗时: 10.513s
[2025-07-24 01:49:13] x方向算符进度: 18/100, 当前算符: D_(0, 1) * D_(11, 90), 耗时: 10.537s
[2025-07-24 01:49:23] x方向算符进度: 19/100, 当前算符: D_(0, 1) * D_(12, 13), 耗时: 10.561s
[2025-07-24 01:49:34] x方向算符进度: 20/100, 当前算符: D_(0, 1) * D_(12, 93), 耗时: 10.508s
[2025-07-24 01:49:44] x方向算符进度: 21/100, 当前算符: D_(0, 1) * D_(13, 32), 耗时: 10.568s
[2025-07-24 01:49:55] x方向算符进度: 22/100, 当前算符: D_(0, 1) * D_(14, 15), 耗时: 10.520s
[2025-07-24 01:50:06] x方向算符进度: 23/100, 当前算符: D_(0, 1) * D_(14, 35), 耗时: 10.560s
[2025-07-24 01:50:16] x方向算符进度: 24/100, 当前算符: D_(0, 1) * D_(15, 94), 耗时: 10.524s
[2025-07-24 01:50:27] x方向算符进度: 25/100, 当前算符: D_(0, 1) * D_(16, 17), 耗时: 10.567s
[2025-07-24 01:50:37] x方向算符进度: 26/100, 当前算符: D_(0, 1) * D_(16, 97), 耗时: 10.559s
[2025-07-24 01:50:48] x方向算符进度: 27/100, 当前算符: D_(0, 1) * D_(17, 36), 耗时: 10.489s
[2025-07-24 01:50:58] x方向算符进度: 28/100, 当前算符: D_(0, 1) * D_(18, 19), 耗时: 10.566s
[2025-07-24 01:51:09] x方向算符进度: 29/100, 当前算符: D_(0, 1) * D_(18, 39), 耗时: 10.515s
[2025-07-24 01:51:19] x方向算符进度: 30/100, 当前算符: D_(0, 1) * D_(19, 98), 耗时: 10.566s
[2025-07-24 01:51:30] x方向算符进度: 31/100, 当前算符: D_(0, 1) * D_(20, 21), 耗时: 10.559s
[2025-07-24 01:51:40] x方向算符进度: 32/100, 当前算符: D_(0, 1) * D_(21, 40), 耗时: 10.480s
[2025-07-24 01:51:51] x方向算符进度: 33/100, 当前算符: D_(0, 1) * D_(22, 23), 耗时: 10.563s
[2025-07-24 01:52:01] x方向算符进度: 34/100, 当前算符: D_(0, 1) * D_(22, 43), 耗时: 10.509s
[2025-07-24 01:52:12] x方向算符进度: 35/100, 当前算符: D_(0, 1) * D_(24, 25), 耗时: 10.562s
[2025-07-24 01:52:23] x方向算符进度: 36/100, 当前算符: D_(0, 1) * D_(25, 44), 耗时: 10.509s
[2025-07-24 01:52:33] x方向算符进度: 37/100, 当前算符: D_(0, 1) * D_(26, 27), 耗时: 10.550s
[2025-07-24 01:52:44] x方向算符进度: 38/100, 当前算符: D_(0, 1) * D_(26, 47), 耗时: 10.515s
[2025-07-24 01:52:54] x方向算符进度: 39/100, 当前算符: D_(0, 1) * D_(28, 29), 耗时: 10.563s
[2025-07-24 01:53:05] x方向算符进度: 40/100, 当前算符: D_(0, 1) * D_(29, 48), 耗时: 10.510s
[2025-07-24 01:53:15] x方向算符进度: 41/100, 当前算符: D_(0, 1) * D_(30, 31), 耗时: 10.555s
[2025-07-24 01:53:26] x方向算符进度: 42/100, 当前算符: D_(0, 1) * D_(30, 51), 耗时: 10.526s
[2025-07-24 01:53:36] x方向算符进度: 43/100, 当前算符: D_(0, 1) * D_(32, 33), 耗时: 10.527s
[2025-07-24 01:53:47] x方向算符进度: 44/100, 当前算符: D_(0, 1) * D_(33, 52), 耗时: 10.562s
[2025-07-24 01:53:57] x方向算符进度: 45/100, 当前算符: D_(0, 1) * D_(34, 35), 耗时: 10.559s
[2025-07-24 01:54:08] x方向算符进度: 46/100, 当前算符: D_(0, 1) * D_(34, 55), 耗时: 10.554s
[2025-07-24 01:54:19] x方向算符进度: 47/100, 当前算符: D_(0, 1) * D_(36, 37), 耗时: 10.564s
[2025-07-24 01:54:29] x方向算符进度: 48/100, 当前算符: D_(0, 1) * D_(37, 56), 耗时: 10.509s
[2025-07-24 01:54:40] x方向算符进度: 49/100, 当前算符: D_(0, 1) * D_(38, 39), 耗时: 10.560s
[2025-07-24 01:54:50] x方向算符进度: 50/100, 当前算符: D_(0, 1) * D_(38, 59), 耗时: 10.513s
[2025-07-24 01:55:01] x方向算符进度: 51/100, 当前算符: D_(0, 1) * D_(40, 41), 耗时: 10.559s
[2025-07-24 01:55:11] x方向算符进度: 52/100, 当前算符: D_(0, 1) * D_(41, 60), 耗时: 10.509s
[2025-07-24 01:55:22] x方向算符进度: 53/100, 当前算符: D_(0, 1) * D_(42, 43), 耗时: 10.564s
[2025-07-24 01:55:32] x方向算符进度: 54/100, 当前算符: D_(0, 1) * D_(42, 63), 耗时: 10.523s
[2025-07-24 01:55:43] x方向算符进度: 55/100, 当前算符: D_(0, 1) * D_(44, 45), 耗时: 10.567s
[2025-07-24 01:55:53] x方向算符进度: 56/100, 当前算符: D_(0, 1) * D_(45, 64), 耗时: 10.493s
[2025-07-24 01:56:04] x方向算符进度: 57/100, 当前算符: D_(0, 1) * D_(46, 47), 耗时: 10.566s
[2025-07-24 01:56:14] x方向算符进度: 58/100, 当前算符: D_(0, 1) * D_(46, 67), 耗时: 10.515s
[2025-07-24 01:56:25] x方向算符进度: 59/100, 当前算符: D_(0, 1) * D_(48, 49), 耗时: 10.551s
[2025-07-24 01:56:36] x方向算符进度: 60/100, 当前算符: D_(0, 1) * D_(49, 68), 耗时: 10.519s
[2025-07-24 01:56:46] x方向算符进度: 61/100, 当前算符: D_(0, 1) * D_(50, 51), 耗时: 10.560s
[2025-07-24 01:56:57] x方向算符进度: 62/100, 当前算符: D_(0, 1) * D_(50, 71), 耗时: 10.536s
[2025-07-24 01:57:07] x方向算符进度: 63/100, 当前算符: D_(0, 1) * D_(52, 53), 耗时: 10.538s
[2025-07-24 01:57:18] x方向算符进度: 64/100, 当前算符: D_(0, 1) * D_(53, 72), 耗时: 10.552s
[2025-07-24 01:57:28] x方向算符进度: 65/100, 当前算符: D_(0, 1) * D_(54, 55), 耗时: 10.524s
[2025-07-24 01:57:39] x方向算符进度: 66/100, 当前算符: D_(0, 1) * D_(54, 75), 耗时: 10.557s
[2025-07-24 01:57:49] x方向算符进度: 67/100, 当前算符: D_(0, 1) * D_(56, 57), 耗时: 10.508s
[2025-07-24 01:58:00] x方向算符进度: 68/100, 当前算符: D_(0, 1) * D_(57, 76), 耗时: 10.560s
[2025-07-24 01:58:10] x方向算符进度: 69/100, 当前算符: D_(0, 1) * D_(58, 59), 耗时: 10.555s
[2025-07-24 01:58:21] x方向算符进度: 70/100, 当前算符: D_(0, 1) * D_(58, 79), 耗时: 10.520s
[2025-07-24 01:58:31] x方向算符进度: 71/100, 当前算符: D_(0, 1) * D_(60, 61), 耗时: 10.557s
[2025-07-24 01:58:42] x方向算符进度: 72/100, 当前算符: D_(0, 1) * D_(61, 80), 耗时: 10.527s
[2025-07-24 01:58:53] x方向算符进度: 73/100, 当前算符: D_(0, 1) * D_(62, 63), 耗时: 10.552s
[2025-07-24 01:59:03] x方向算符进度: 74/100, 当前算符: D_(0, 1) * D_(62, 83), 耗时: 10.537s
[2025-07-24 01:59:14] x方向算符进度: 75/100, 当前算符: D_(0, 1) * D_(64, 65), 耗时: 10.564s
[2025-07-24 01:59:24] x方向算符进度: 76/100, 当前算符: D_(0, 1) * D_(65, 84), 耗时: 10.520s
[2025-07-24 01:59:35] x方向算符进度: 77/100, 当前算符: D_(0, 1) * D_(66, 67), 耗时: 10.568s
[2025-07-24 01:59:45] x方向算符进度: 78/100, 当前算符: D_(0, 1) * D_(66, 87), 耗时: 10.507s
[2025-07-24 01:59:56] x方向算符进度: 79/100, 当前算符: D_(0, 1) * D_(68, 69), 耗时: 10.510s
[2025-07-24 02:00:06] x方向算符进度: 80/100, 当前算符: D_(0, 1) * D_(69, 88), 耗时: 10.568s
[2025-07-24 02:00:17] x方向算符进度: 81/100, 当前算符: D_(0, 1) * D_(70, 71), 耗时: 10.510s
[2025-07-24 02:00:27] x方向算符进度: 82/100, 当前算符: D_(0, 1) * D_(70, 91), 耗时: 10.569s
[2025-07-24 02:00:38] x方向算符进度: 83/100, 当前算符: D_(0, 1) * D_(72, 73), 耗时: 10.519s
[2025-07-24 02:00:48] x方向算符进度: 84/100, 当前算符: D_(0, 1) * D_(73, 92), 耗时: 10.550s
[2025-07-24 02:00:59] x方向算符进度: 85/100, 当前算符: D_(0, 1) * D_(74, 75), 耗时: 10.512s
[2025-07-24 02:01:10] x方向算符进度: 86/100, 当前算符: D_(0, 1) * D_(74, 95), 耗时: 10.504s
[2025-07-24 02:01:20] x方向算符进度: 87/100, 当前算符: D_(0, 1) * D_(76, 77), 耗时: 10.562s
[2025-07-24 02:01:31] x方向算符进度: 88/100, 当前算符: D_(0, 1) * D_(77, 96), 耗时: 10.510s
[2025-07-24 02:01:41] x方向算符进度: 89/100, 当前算符: D_(0, 1) * D_(78, 79), 耗时: 10.562s
[2025-07-24 02:01:52] x方向算符进度: 90/100, 当前算符: D_(0, 1) * D_(78, 99), 耗时: 10.517s
[2025-07-24 02:02:02] x方向算符进度: 91/100, 当前算符: D_(0, 1) * D_(80, 81), 耗时: 10.572s
[2025-07-24 02:02:13] x方向算符进度: 92/100, 当前算符: D_(0, 1) * D_(82, 83), 耗时: 10.515s
[2025-07-24 02:02:23] x方向算符进度: 93/100, 当前算符: D_(0, 1) * D_(84, 85), 耗时: 10.569s
[2025-07-24 02:02:34] x方向算符进度: 94/100, 当前算符: D_(0, 1) * D_(86, 87), 耗时: 10.510s
[2025-07-24 02:02:44] x方向算符进度: 95/100, 当前算符: D_(0, 1) * D_(88, 89), 耗时: 10.528s
[2025-07-24 02:02:55] x方向算符进度: 96/100, 当前算符: D_(0, 1) * D_(90, 91), 耗时: 10.570s
[2025-07-24 02:03:05] x方向算符进度: 97/100, 当前算符: D_(0, 1) * D_(92, 93), 耗时: 10.528s
[2025-07-24 02:03:16] x方向算符进度: 98/100, 当前算符: D_(0, 1) * D_(94, 95), 耗时: 10.563s
[2025-07-24 02:03:27] x方向算符进度: 99/100, 当前算符: D_(0, 1) * D_(96, 97), 耗时: 10.510s
[2025-07-24 02:03:37] x方向算符进度: 100/100, 当前算符: D_(0, 1) * D_(98, 99), 耗时: 10.569s
[2025-07-24 02:03:37] x方向二聚体相关函数计算完成,耗时: 1062.25 秒
[2025-07-24 02:03:37] --------------------------------------------------------------------------------
[2025-07-24 02:03:37] 使用y-二聚体 (0, 3) (全局索引 100) 作为参考点, 计算y方向连通二聚体相关函数...
[2025-07-24 02:03:43] y方向算符进度: 1/100, 当前算符: D_(0, 3) * D_(0, 3), 耗时: 6.287s
[2025-07-24 02:03:52] y方向算符进度: 2/100, 当前算符: D_(0, 3) * D_(0, 19), 耗时: 8.790s
[2025-07-24 02:04:03] y方向算符进度: 3/100, 当前算符: D_(0, 3) * D_(1, 2), 耗时: 10.586s
[2025-07-24 02:04:13] y方向算符进度: 4/100, 当前算符: D_(0, 3) * D_(1, 18), 耗时: 10.550s
[2025-07-24 02:04:24] y方向算符进度: 5/100, 当前算符: D_(0, 3) * D_(2, 5), 耗时: 10.549s
[2025-07-24 02:04:33] y方向算符进度: 6/100, 当前算符: D_(0, 3) * D_(3, 4), 耗时: 8.785s
[2025-07-24 02:04:43] y方向算符进度: 7/100, 当前算符: D_(0, 3) * D_(4, 7), 耗时: 10.545s
[2025-07-24 02:04:54] y方向算符进度: 8/100, 当前算符: D_(0, 3) * D_(5, 6), 耗时: 10.585s
[2025-07-24 02:05:04] y方向算符进度: 9/100, 当前算符: D_(0, 3) * D_(6, 9), 耗时: 10.549s
[2025-07-24 02:05:15] y方向算符进度: 10/100, 当前算符: D_(0, 3) * D_(7, 8), 耗时: 10.584s
[2025-07-24 02:05:25] y方向算符进度: 11/100, 当前算符: D_(0, 3) * D_(8, 11), 耗时: 10.543s
[2025-07-24 02:05:36] y方向算符进度: 12/100, 当前算符: D_(0, 3) * D_(9, 10), 耗时: 10.586s
[2025-07-24 02:05:47] y方向算符进度: 13/100, 当前算符: D_(0, 3) * D_(10, 13), 耗时: 10.545s
[2025-07-24 02:05:57] y方向算符进度: 14/100, 当前算符: D_(0, 3) * D_(11, 12), 耗时: 10.584s
[2025-07-24 02:06:08] y方向算符进度: 15/100, 当前算符: D_(0, 3) * D_(12, 15), 耗时: 10.555s
[2025-07-24 02:06:18] y方向算符进度: 16/100, 当前算符: D_(0, 3) * D_(13, 14), 耗时: 10.586s
[2025-07-24 02:06:29] y方向算符进度: 17/100, 当前算符: D_(0, 3) * D_(14, 17), 耗时: 10.552s
[2025-07-24 02:06:39] y方向算符进度: 18/100, 当前算符: D_(0, 3) * D_(15, 16), 耗时: 10.529s
[2025-07-24 02:06:50] y方向算符进度: 19/100, 当前算符: D_(0, 3) * D_(16, 19), 耗时: 10.582s
[2025-07-24 02:07:01] y方向算符进度: 20/100, 当前算符: D_(0, 3) * D_(17, 18), 耗时: 10.545s
[2025-07-24 02:07:11] y方向算符进度: 21/100, 当前算符: D_(0, 3) * D_(20, 23), 耗时: 10.584s
[2025-07-24 02:07:22] y方向算符进度: 22/100, 当前算符: D_(0, 3) * D_(20, 39), 耗时: 10.545s
[2025-07-24 02:07:32] y方向算符进度: 23/100, 当前算符: D_(0, 3) * D_(21, 22), 耗时: 10.582s
[2025-07-24 02:07:43] y方向算符进度: 24/100, 当前算符: D_(0, 3) * D_(21, 38), 耗时: 10.551s
[2025-07-24 02:07:53] y方向算符进度: 25/100, 当前算符: D_(0, 3) * D_(22, 25), 耗时: 10.588s
[2025-07-24 02:08:04] y方向算符进度: 26/100, 当前算符: D_(0, 3) * D_(23, 24), 耗时: 10.531s
[2025-07-24 02:08:15] y方向算符进度: 27/100, 当前算符: D_(0, 3) * D_(24, 27), 耗时: 10.582s
[2025-07-24 02:08:25] y方向算符进度: 28/100, 当前算符: D_(0, 3) * D_(25, 26), 耗时: 10.544s
[2025-07-24 02:08:36] y方向算符进度: 29/100, 当前算符: D_(0, 3) * D_(26, 29), 耗时: 10.583s
[2025-07-24 02:08:46] y方向算符进度: 30/100, 当前算符: D_(0, 3) * D_(27, 28), 耗时: 10.585s
[2025-07-24 02:08:57] y方向算符进度: 31/100, 当前算符: D_(0, 3) * D_(28, 31), 耗时: 10.553s
[2025-07-24 02:09:07] y方向算符进度: 32/100, 当前算符: D_(0, 3) * D_(29, 30), 耗时: 10.584s
[2025-07-24 02:09:18] y方向算符进度: 33/100, 当前算符: D_(0, 3) * D_(30, 33), 耗时: 10.551s
[2025-07-24 02:09:28] y方向算符进度: 34/100, 当前算符: D_(0, 3) * D_(31, 32), 耗时: 10.551s
[2025-07-24 02:09:39] y方向算符进度: 35/100, 当前算符: D_(0, 3) * D_(32, 35), 耗时: 10.586s
[2025-07-24 02:09:50] y方向算符进度: 36/100, 当前算符: D_(0, 3) * D_(33, 34), 耗时: 10.541s
[2025-07-24 02:10:00] y方向算符进度: 37/100, 当前算符: D_(0, 3) * D_(34, 37), 耗时: 10.586s
[2025-07-24 02:10:11] y方向算符进度: 38/100, 当前算符: D_(0, 3) * D_(35, 36), 耗时: 10.530s
[2025-07-24 02:10:21] y方向算符进度: 39/100, 当前算符: D_(0, 3) * D_(36, 39), 耗时: 10.582s
[2025-07-24 02:10:32] y方向算符进度: 40/100, 当前算符: D_(0, 3) * D_(37, 38), 耗时: 10.545s
[2025-07-24 02:10:42] y方向算符进度: 41/100, 当前算符: D_(0, 3) * D_(40, 43), 耗时: 10.581s
[2025-07-24 02:10:53] y方向算符进度: 42/100, 当前算符: D_(0, 3) * D_(40, 59), 耗时: 10.551s
[2025-07-24 02:11:04] y方向算符进度: 43/100, 当前算符: D_(0, 3) * D_(41, 42), 耗时: 10.582s
[2025-07-24 02:11:14] y方向算符进度: 44/100, 当前算符: D_(0, 3) * D_(41, 58), 耗时: 10.550s
[2025-07-24 02:11:25] y方向算符进度: 45/100, 当前算符: D_(0, 3) * D_(42, 45), 耗时: 10.587s
[2025-07-24 02:11:35] y方向算符进度: 46/100, 当前算符: D_(0, 3) * D_(43, 44), 耗时: 10.585s
[2025-07-24 02:11:46] y方向算符进度: 47/100, 当前算符: D_(0, 3) * D_(44, 47), 耗时: 10.549s
[2025-07-24 02:11:56] y方向算符进度: 48/100, 当前算符: D_(0, 3) * D_(45, 46), 耗时: 10.582s
[2025-07-24 02:12:07] y方向算符进度: 49/100, 当前算符: D_(0, 3) * D_(46, 49), 耗时: 10.538s
[2025-07-24 02:12:18] y方向算符进度: 50/100, 当前算符: D_(0, 3) * D_(47, 48), 耗时: 10.583s
[2025-07-24 02:12:28] y方向算符进度: 51/100, 当前算符: D_(0, 3) * D_(48, 51), 耗时: 10.584s
[2025-07-24 02:12:39] y方向算符进度: 52/100, 当前算符: D_(0, 3) * D_(49, 50), 耗时: 10.551s
[2025-07-24 02:12:49] y方向算符进度: 53/100, 当前算符: D_(0, 3) * D_(50, 53), 耗时: 10.586s
[2025-07-24 02:13:00] y方向算符进度: 54/100, 当前算符: D_(0, 3) * D_(51, 52), 耗时: 10.551s
[2025-07-24 02:13:10] y方向算符进度: 55/100, 当前算符: D_(0, 3) * D_(52, 55), 耗时: 10.581s
[2025-07-24 02:13:21] y方向算符进度: 56/100, 当前算符: D_(0, 3) * D_(53, 54), 耗时: 10.557s
[2025-07-24 02:13:32] y方向算符进度: 57/100, 当前算符: D_(0, 3) * D_(54, 57), 耗时: 10.586s
[2025-07-24 02:13:42] y方向算符进度: 58/100, 当前算符: D_(0, 3) * D_(55, 56), 耗时: 10.544s
[2025-07-24 02:13:53] y方向算符进度: 59/100, 当前算符: D_(0, 3) * D_(56, 59), 耗时: 10.583s
[2025-07-24 02:14:03] y方向算符进度: 60/100, 当前算符: D_(0, 3) * D_(57, 58), 耗时: 10.550s
[2025-07-24 02:14:14] y方向算符进度: 61/100, 当前算符: D_(0, 3) * D_(60, 63), 耗时: 10.585s
[2025-07-24 02:14:24] y方向算符进度: 62/100, 当前算符: D_(0, 3) * D_(60, 79), 耗时: 10.581s
[2025-07-24 02:14:35] y方向算符进度: 63/100, 当前算符: D_(0, 3) * D_(61, 62), 耗时: 10.543s
[2025-07-24 02:14:46] y方向算符进度: 64/100, 当前算符: D_(0, 3) * D_(61, 78), 耗时: 10.582s
[2025-07-24 02:14:56] y方向算符进度: 65/100, 当前算符: D_(0, 3) * D_(62, 65), 耗时: 10.547s
[2025-07-24 02:15:07] y方向算符进度: 66/100, 当前算符: D_(0, 3) * D_(63, 64), 耗时: 10.583s
[2025-07-24 02:15:17] y方向算符进度: 67/100, 当前算符: D_(0, 3) * D_(64, 67), 耗时: 10.555s
[2025-07-24 02:15:28] y方向算符进度: 68/100, 当前算符: D_(0, 3) * D_(65, 66), 耗时: 10.548s
[2025-07-24 02:15:38] y方向算符进度: 69/100, 当前算符: D_(0, 3) * D_(66, 69), 耗时: 10.587s
[2025-07-24 02:15:49] y方向算符进度: 70/100, 当前算符: D_(0, 3) * D_(67, 68), 耗时: 10.545s
[2025-07-24 02:15:59] y方向算符进度: 71/100, 当前算符: D_(0, 3) * D_(68, 71), 耗时: 10.586s
[2025-07-24 02:16:10] y方向算符进度: 72/100, 当前算符: D_(0, 3) * D_(69, 70), 耗时: 10.541s
[2025-07-24 02:16:21] y方向算符进度: 73/100, 当前算符: D_(0, 3) * D_(70, 73), 耗时: 10.585s
[2025-07-24 02:16:31] y方向算符进度: 74/100, 当前算符: D_(0, 3) * D_(71, 72), 耗时: 10.552s
[2025-07-24 02:16:42] y方向算符进度: 75/100, 当前算符: D_(0, 3) * D_(72, 75), 耗时: 10.586s
[2025-07-24 02:16:52] y方向算符进度: 76/100, 当前算符: D_(0, 3) * D_(73, 74), 耗时: 10.555s
[2025-07-24 02:17:03] y方向算符进度: 77/100, 当前算符: D_(0, 3) * D_(74, 77), 耗时: 10.586s
[2025-07-24 02:17:13] y方向算符进度: 78/100, 当前算符: D_(0, 3) * D_(75, 76), 耗时: 10.585s
[2025-07-24 02:17:24] y方向算符进度: 79/100, 当前算符: D_(0, 3) * D_(76, 79), 耗时: 10.565s
[2025-07-24 02:17:35] y方向算符进度: 80/100, 当前算符: D_(0, 3) * D_(77, 78), 耗时: 10.583s
[2025-07-24 02:17:45] y方向算符进度: 81/100, 当前算符: D_(0, 3) * D_(80, 83), 耗时: 10.550s
[2025-07-24 02:17:56] y方向算符进度: 82/100, 当前算符: D_(0, 3) * D_(80, 99), 耗时: 10.582s
[2025-07-24 02:18:06] y方向算符进度: 83/100, 当前算符: D_(0, 3) * D_(81, 82), 耗时: 10.544s
[2025-07-24 02:18:17] y方向算符进度: 84/100, 当前算符: D_(0, 3) * D_(81, 98), 耗时: 10.584s
[2025-07-24 02:18:27] y方向算符进度: 85/100, 当前算符: D_(0, 3) * D_(82, 85), 耗时: 10.547s
[2025-07-24 02:18:38] y方向算符进度: 86/100, 当前算符: D_(0, 3) * D_(83, 84), 耗时: 10.584s
[2025-07-24 02:18:49] y方向算符进度: 87/100, 当前算符: D_(0, 3) * D_(84, 87), 耗时: 10.546s
[2025-07-24 02:18:59] y方向算符进度: 88/100, 当前算符: D_(0, 3) * D_(85, 86), 耗时: 10.587s
[2025-07-24 02:19:10] y方向算符进度: 89/100, 当前算符: D_(0, 3) * D_(86, 89), 耗时: 10.550s
[2025-07-24 02:19:20] y方向算符进度: 90/100, 当前算符: D_(0, 3) * D_(87, 88), 耗时: 10.583s
[2025-07-24 02:19:31] y方向算符进度: 91/100, 当前算符: D_(0, 3) * D_(88, 91), 耗时: 10.541s
[2025-07-24 02:19:41] y方向算符进度: 92/100, 当前算符: D_(0, 3) * D_(89, 90), 耗时: 10.582s
[2025-07-24 02:19:52] y方向算符进度: 93/100, 当前算符: D_(0, 3) * D_(90, 93), 耗时: 10.548s
[2025-07-24 02:20:03] y方向算符进度: 94/100, 当前算符: D_(0, 3) * D_(91, 92), 耗时: 10.555s
[2025-07-24 02:20:13] y方向算符进度: 95/100, 当前算符: D_(0, 3) * D_(92, 95), 耗时: 10.586s
[2025-07-24 02:20:24] y方向算符进度: 96/100, 当前算符: D_(0, 3) * D_(93, 94), 耗时: 10.549s
[2025-07-24 02:20:34] y方向算符进度: 97/100, 当前算符: D_(0, 3) * D_(94, 97), 耗时: 10.582s
[2025-07-24 02:20:45] y方向算符进度: 98/100, 当前算符: D_(0, 3) * D_(95, 96), 耗时: 10.549s
[2025-07-24 02:20:55] y方向算符进度: 99/100, 当前算符: D_(0, 3) * D_(96, 99), 耗时: 10.585s
[2025-07-24 02:21:06] y方向算符进度: 100/100, 当前算符: D_(0, 3) * D_(97, 98), 耗时: 10.543s
[2025-07-24 02:21:06] y方向二聚体相关函数计算完成,耗时: 1048.81 秒
[2025-07-24 02:21:06] 计算傅里叶变换...
[2025-07-24 02:21:07] 二聚体结构因子计算完成
[2025-07-24 02:21:08] 二聚体相关函数平均误差: 0.000556
[2025-07-24 02:21:08] 恢复原始样本数: 4096
[2025-07-24 02:21:08] ================================================================================
[2025-07-24 02:21:08] 所有分析完成
