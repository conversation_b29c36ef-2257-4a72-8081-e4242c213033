[2025-07-24 04:50:26] ================================================================================
[2025-07-24 04:50:26] 加载量子态: L=5, J2=0.00, J1=0.09
[2025-07-24 04:50:26] 设置样本数为: 1048576
[2025-07-24 04:50:26] 开始生成共享样本集...
[2025-07-24 04:53:46] 样本生成完成,耗时: 199.541 秒
[2025-07-24 04:53:46] ================================================================================
[2025-07-24 04:53:46] 开始计算自旋结构因子...
[2025-07-24 04:53:46] 初始化操作符缓存...
[2025-07-24 04:53:46] 预构建所有自旋相关操作符...
[2025-07-24 04:53:46] 开始计算自旋相关函数...
[2025-07-24 04:53:56] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 10.430s
[2025-07-24 04:54:08] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 12.127s
[2025-07-24 04:54:15] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.275s
[2025-07-24 04:54:21] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.306s
[2025-07-24 04:54:27] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.274s
[2025-07-24 04:54:34] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.306s
[2025-07-24 04:54:40] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.305s
[2025-07-24 04:54:46] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.274s
[2025-07-24 04:54:52] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.306s
[2025-07-24 04:54:59] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.274s
[2025-07-24 04:55:05] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.305s
[2025-07-24 04:55:11] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.275s
[2025-07-24 04:55:18] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.305s
[2025-07-24 04:55:24] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.305s
[2025-07-24 04:55:30] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.306s
[2025-07-24 04:55:37] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.274s
[2025-07-24 04:55:43] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.306s
[2025-07-24 04:55:49] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.274s
[2025-07-24 04:55:55] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.274s
[2025-07-24 04:56:02] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.306s
[2025-07-24 04:56:08] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.274s
[2025-07-24 04:56:14] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.275s
[2025-07-24 04:56:21] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.306s
[2025-07-24 04:56:27] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.274s
[2025-07-24 04:56:33] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.306s
[2025-07-24 04:56:39] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.306s
[2025-07-24 04:56:46] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.275s
[2025-07-24 04:56:52] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.305s
[2025-07-24 04:56:58] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.273s
[2025-07-24 04:57:05] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.274s
[2025-07-24 04:57:11] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.309s
[2025-07-24 04:57:17] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.306s
[2025-07-24 04:57:23] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.273s
[2025-07-24 04:57:30] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.274s
[2025-07-24 04:57:36] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.304s
[2025-07-24 04:57:42] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.273s
[2025-07-24 04:57:49] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.307s
[2025-07-24 04:57:55] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.305s
[2025-07-24 04:58:01] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.276s
[2025-07-24 04:58:08] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.304s
[2025-07-24 04:58:14] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.275s
[2025-07-24 04:58:20] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.275s
[2025-07-24 04:58:26] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.304s
[2025-07-24 04:58:33] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.275s
[2025-07-24 04:58:39] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.304s
[2025-07-24 04:58:45] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.305s
[2025-07-24 04:58:52] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.277s
[2025-07-24 04:58:58] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.306s
[2025-07-24 04:59:04] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.276s
[2025-07-24 04:59:10] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.273s
[2025-07-24 04:59:17] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.305s
[2025-07-24 04:59:23] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.274s
[2025-07-24 04:59:29] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.275s
[2025-07-24 04:59:36] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.274s
[2025-07-24 04:59:42] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.304s
[2025-07-24 04:59:48] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.274s
[2025-07-24 04:59:54] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.304s
[2025-07-24 05:00:01] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.305s
[2025-07-24 05:00:07] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.276s
[2025-07-24 05:00:13] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.305s
[2025-07-24 05:00:20] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.273s
[2025-07-24 05:00:26] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.273s
[2025-07-24 05:00:32] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.371s
[2025-07-24 05:00:39] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.275s
[2025-07-24 05:00:45] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.304s
[2025-07-24 05:00:51] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.305s
[2025-07-24 05:00:57] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.306s
[2025-07-24 05:01:04] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.305s
[2025-07-24 05:01:10] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.275s
[2025-07-24 05:01:16] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.275s
[2025-07-24 05:01:23] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.306s
[2025-07-24 05:01:29] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.274s
[2025-07-24 05:01:35] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.305s
[2025-07-24 05:01:41] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.305s
[2025-07-24 05:01:48] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.275s
[2025-07-24 05:01:54] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.306s
[2025-07-24 05:02:00] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.274s
[2025-07-24 05:02:07] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.274s
[2025-07-24 05:02:13] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.306s
[2025-07-24 05:02:19] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.275s
[2025-07-24 05:02:26] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.305s
[2025-07-24 05:02:32] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.305s
[2025-07-24 05:02:38] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.275s
[2025-07-24 05:02:44] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.306s
[2025-07-24 05:02:51] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.304s
[2025-07-24 05:02:57] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.304s
[2025-07-24 05:03:03] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.276s
[2025-07-24 05:03:10] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.305s
[2025-07-24 05:03:16] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.274s
[2025-07-24 05:03:22] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.275s
[2025-07-24 05:03:28] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.304s
[2025-07-24 05:03:35] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.276s
[2025-07-24 05:03:41] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.305s
[2025-07-24 05:03:47] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.304s
[2025-07-24 05:03:54] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.273s
[2025-07-24 05:04:00] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.309s
[2025-07-24 05:04:06] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.277s
[2025-07-24 05:04:12] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.276s
[2025-07-24 05:04:19] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.304s
[2025-07-24 05:04:25] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.274s
[2025-07-24 05:04:25] 自旋相关函数计算完成,总耗时 639.19 秒
[2025-07-24 05:04:25] 计算傅里叶变换...
[2025-07-24 05:04:26] 自旋结构因子计算完成
[2025-07-24 05:04:27] 自旋相关函数平均误差: 0.000734
[2025-07-24 05:04:27] ================================================================================
[2025-07-24 05:04:27] 开始计算二聚体结构因子...
[2025-07-24 05:04:27] 识别x和y方向的二聚体...
[2025-07-24 05:04:27] 找到 100 个x方向二聚体和 100 个y方向二聚体
[2025-07-24 05:04:27] 预计算二聚体操作符...
[2025-07-24 05:04:28] 使用x-二聚体 (0, 1) (全局索引 0) 作为参考点, 计算x方向连通二聚体相关函数...
[2025-07-24 05:04:34] x方向算符进度: 1/100, 当前算符: D_(0, 1) * D_(0, 1), 耗时: 6.340s
[2025-07-24 05:04:53] x方向算符进度: 2/100, 当前算符: D_(0, 1) * D_(0, 81), 耗时: 18.994s
[2025-07-24 05:05:02] x方向算符进度: 3/100, 当前算符: D_(0, 1) * D_(1, 20), 耗时: 8.788s
[2025-07-24 05:05:18] x方向算符进度: 4/100, 当前算符: D_(0, 1) * D_(2, 3), 耗时: 16.359s
[2025-07-24 05:05:29] x方向算符进度: 5/100, 当前算符: D_(0, 1) * D_(2, 23), 耗时: 10.542s
[2025-07-24 05:05:40] x方向算符进度: 6/100, 当前算符: D_(0, 1) * D_(3, 82), 耗时: 10.600s
[2025-07-24 05:05:50] x方向算符进度: 7/100, 当前算符: D_(0, 1) * D_(4, 5), 耗时: 10.542s
[2025-07-24 05:06:01] x方向算符进度: 8/100, 当前算符: D_(0, 1) * D_(4, 85), 耗时: 10.603s
[2025-07-24 05:06:11] x方向算符进度: 9/100, 当前算符: D_(0, 1) * D_(5, 24), 耗时: 10.603s
[2025-07-24 05:06:22] x方向算符进度: 10/100, 当前算符: D_(0, 1) * D_(6, 7), 耗时: 10.541s
[2025-07-24 05:06:32] x方向算符进度: 11/100, 当前算符: D_(0, 1) * D_(6, 27), 耗时: 10.602s
[2025-07-24 05:06:43] x方向算符进度: 12/100, 当前算符: D_(0, 1) * D_(7, 86), 耗时: 10.602s
[2025-07-24 05:06:54] x方向算符进度: 13/100, 当前算符: D_(0, 1) * D_(8, 9), 耗时: 10.542s
[2025-07-24 05:07:04] x方向算符进度: 14/100, 当前算符: D_(0, 1) * D_(8, 89), 耗时: 10.600s
[2025-07-24 05:07:15] x方向算符进度: 15/100, 当前算符: D_(0, 1) * D_(9, 28), 耗时: 10.539s
[2025-07-24 05:07:25] x方向算符进度: 16/100, 当前算符: D_(0, 1) * D_(10, 11), 耗时: 10.599s
[2025-07-24 05:07:36] x方向算符进度: 17/100, 当前算符: D_(0, 1) * D_(10, 31), 耗时: 10.541s
[2025-07-24 05:07:46] x方向算符进度: 18/100, 当前算符: D_(0, 1) * D_(11, 90), 耗时: 10.542s
[2025-07-24 05:07:57] x方向算符进度: 19/100, 当前算符: D_(0, 1) * D_(12, 13), 耗时: 10.599s
[2025-07-24 05:08:08] x方向算符进度: 20/100, 当前算符: D_(0, 1) * D_(12, 93), 耗时: 10.542s
[2025-07-24 05:08:18] x方向算符进度: 21/100, 当前算符: D_(0, 1) * D_(13, 32), 耗时: 10.599s
[2025-07-24 05:08:29] x方向算符进度: 22/100, 当前算符: D_(0, 1) * D_(14, 15), 耗时: 10.540s
[2025-07-24 05:08:39] x方向算符进度: 23/100, 当前算符: D_(0, 1) * D_(14, 35), 耗时: 10.601s
[2025-07-24 05:08:50] x方向算符进度: 24/100, 当前算符: D_(0, 1) * D_(15, 94), 耗时: 10.542s
[2025-07-24 05:09:00] x方向算符进度: 25/100, 当前算符: D_(0, 1) * D_(16, 17), 耗时: 10.603s
[2025-07-24 05:09:11] x方向算符进度: 26/100, 当前算符: D_(0, 1) * D_(16, 97), 耗时: 10.600s
[2025-07-24 05:09:22] x方向算符进度: 27/100, 当前算符: D_(0, 1) * D_(17, 36), 耗时: 10.541s
[2025-07-24 05:09:32] x方向算符进度: 28/100, 当前算符: D_(0, 1) * D_(18, 19), 耗时: 10.645s
[2025-07-24 05:09:43] x方向算符进度: 29/100, 当前算符: D_(0, 1) * D_(18, 39), 耗时: 10.540s
[2025-07-24 05:09:53] x方向算符进度: 30/100, 当前算符: D_(0, 1) * D_(19, 98), 耗时: 10.602s
[2025-07-24 05:10:04] x方向算符进度: 31/100, 当前算符: D_(0, 1) * D_(20, 21), 耗时: 10.600s
[2025-07-24 05:10:15] x方向算符进度: 32/100, 当前算符: D_(0, 1) * D_(21, 40), 耗时: 10.541s
[2025-07-24 05:10:25] x方向算符进度: 33/100, 当前算符: D_(0, 1) * D_(22, 23), 耗时: 10.689s
[2025-07-24 05:10:36] x方向算符进度: 34/100, 当前算符: D_(0, 1) * D_(22, 43), 耗时: 10.542s
[2025-07-24 05:10:46] x方向算符进度: 35/100, 当前算符: D_(0, 1) * D_(24, 25), 耗时: 10.598s
[2025-07-24 05:10:57] x方向算符进度: 36/100, 当前算符: D_(0, 1) * D_(25, 44), 耗时: 10.541s
[2025-07-24 05:11:08] x方向算符进度: 37/100, 当前算符: D_(0, 1) * D_(26, 27), 耗时: 10.773s
[2025-07-24 05:11:18] x方向算符进度: 38/100, 当前算符: D_(0, 1) * D_(26, 47), 耗时: 10.540s
[2025-07-24 05:11:29] x方向算符进度: 39/100, 当前算符: D_(0, 1) * D_(28, 29), 耗时: 10.600s
[2025-07-24 05:11:39] x方向算符进度: 40/100, 当前算符: D_(0, 1) * D_(29, 48), 耗时: 10.542s
[2025-07-24 05:11:50] x方向算符进度: 41/100, 当前算符: D_(0, 1) * D_(30, 31), 耗时: 10.601s
[2025-07-24 05:12:01] x方向算符进度: 42/100, 当前算符: D_(0, 1) * D_(30, 51), 耗时: 10.540s
[2025-07-24 05:12:11] x方向算符进度: 43/100, 当前算符: D_(0, 1) * D_(32, 33), 耗时: 10.555s
[2025-07-24 05:12:22] x方向算符进度: 44/100, 当前算符: D_(0, 1) * D_(33, 52), 耗时: 10.601s
[2025-07-24 05:12:32] x方向算符进度: 45/100, 当前算符: D_(0, 1) * D_(34, 35), 耗时: 10.577s
[2025-07-24 05:12:43] x方向算符进度: 46/100, 当前算符: D_(0, 1) * D_(34, 55), 耗时: 10.602s
[2025-07-24 05:12:53] x方向算符进度: 47/100, 当前算符: D_(0, 1) * D_(36, 37), 耗时: 10.598s
[2025-07-24 05:13:04] x方向算符进度: 48/100, 当前算符: D_(0, 1) * D_(37, 56), 耗时: 10.541s
[2025-07-24 05:13:15] x方向算符进度: 49/100, 当前算符: D_(0, 1) * D_(38, 39), 耗时: 10.599s
[2025-07-24 05:13:25] x方向算符进度: 50/100, 当前算符: D_(0, 1) * D_(38, 59), 耗时: 10.627s
[2025-07-24 05:13:36] x方向算符进度: 51/100, 当前算符: D_(0, 1) * D_(40, 41), 耗时: 10.602s
[2025-07-24 05:13:46] x方向算符进度: 52/100, 当前算符: D_(0, 1) * D_(41, 60), 耗时: 10.542s
[2025-07-24 05:13:57] x方向算符进度: 53/100, 当前算符: D_(0, 1) * D_(42, 43), 耗时: 10.601s
[2025-07-24 05:14:08] x方向算符进度: 54/100, 当前算符: D_(0, 1) * D_(42, 63), 耗时: 10.540s
[2025-07-24 05:14:18] x方向算符进度: 55/100, 当前算符: D_(0, 1) * D_(44, 45), 耗时: 10.603s
[2025-07-24 05:14:29] x方向算符进度: 56/100, 当前算符: D_(0, 1) * D_(45, 64), 耗时: 10.542s
[2025-07-24 05:14:39] x方向算符进度: 57/100, 当前算符: D_(0, 1) * D_(46, 47), 耗时: 10.599s
[2025-07-24 05:14:50] x方向算符进度: 58/100, 当前算符: D_(0, 1) * D_(46, 67), 耗时: 10.540s
[2025-07-24 05:15:00] x方向算符进度: 59/100, 当前算符: D_(0, 1) * D_(48, 49), 耗时: 10.603s
[2025-07-24 05:15:11] x方向算符进度: 60/100, 当前算符: D_(0, 1) * D_(49, 68), 耗时: 10.540s
[2025-07-24 05:15:22] x方向算符进度: 61/100, 当前算符: D_(0, 1) * D_(50, 51), 耗时: 10.600s
[2025-07-24 05:15:32] x方向算符进度: 62/100, 当前算符: D_(0, 1) * D_(50, 71), 耗时: 10.540s
[2025-07-24 05:15:43] x方向算符进度: 63/100, 当前算符: D_(0, 1) * D_(52, 53), 耗时: 10.542s
[2025-07-24 05:15:53] x方向算符进度: 64/100, 当前算符: D_(0, 1) * D_(53, 72), 耗时: 10.602s
[2025-07-24 05:16:04] x方向算符进度: 65/100, 当前算符: D_(0, 1) * D_(54, 55), 耗时: 10.540s
[2025-07-24 05:16:14] x方向算符进度: 66/100, 当前算符: D_(0, 1) * D_(54, 75), 耗时: 10.600s
[2025-07-24 05:16:25] x方向算符进度: 67/100, 当前算符: D_(0, 1) * D_(56, 57), 耗时: 10.541s
[2025-07-24 05:16:36] x方向算符进度: 68/100, 当前算符: D_(0, 1) * D_(57, 76), 耗时: 10.600s
[2025-07-24 05:16:46] x方向算符进度: 69/100, 当前算符: D_(0, 1) * D_(58, 59), 耗时: 10.600s
[2025-07-24 05:16:57] x方向算符进度: 70/100, 当前算符: D_(0, 1) * D_(58, 79), 耗时: 10.540s
[2025-07-24 05:17:07] x方向算符进度: 71/100, 当前算符: D_(0, 1) * D_(60, 61), 耗时: 10.600s
[2025-07-24 05:17:18] x方向算符进度: 72/100, 当前算符: D_(0, 1) * D_(61, 80), 耗时: 10.540s
[2025-07-24 05:17:28] x方向算符进度: 73/100, 当前算符: D_(0, 1) * D_(62, 63), 耗时: 10.602s
[2025-07-24 05:17:39] x方向算符进度: 74/100, 当前算符: D_(0, 1) * D_(62, 83), 耗时: 10.541s
[2025-07-24 05:17:50] x方向算符进度: 75/100, 当前算符: D_(0, 1) * D_(64, 65), 耗时: 10.599s
[2025-07-24 05:18:00] x方向算符进度: 76/100, 当前算符: D_(0, 1) * D_(65, 84), 耗时: 10.540s
[2025-07-24 05:18:11] x方向算符进度: 77/100, 当前算符: D_(0, 1) * D_(66, 67), 耗时: 10.599s
[2025-07-24 05:18:21] x方向算符进度: 78/100, 当前算符: D_(0, 1) * D_(66, 87), 耗时: 10.542s
[2025-07-24 05:18:32] x方向算符进度: 79/100, 当前算符: D_(0, 1) * D_(68, 69), 耗时: 10.542s
[2025-07-24 05:18:42] x方向算符进度: 80/100, 当前算符: D_(0, 1) * D_(69, 88), 耗时: 10.600s
[2025-07-24 05:18:53] x方向算符进度: 81/100, 当前算符: D_(0, 1) * D_(70, 71), 耗时: 10.542s
[2025-07-24 05:19:04] x方向算符进度: 82/100, 当前算符: D_(0, 1) * D_(70, 91), 耗时: 10.600s
[2025-07-24 05:19:14] x方向算符进度: 83/100, 当前算符: D_(0, 1) * D_(72, 73), 耗时: 10.541s
[2025-07-24 05:19:25] x方向算符进度: 84/100, 当前算符: D_(0, 1) * D_(73, 92), 耗时: 10.603s
[2025-07-24 05:19:35] x方向算符进度: 85/100, 当前算符: D_(0, 1) * D_(74, 75), 耗时: 10.541s
[2025-07-24 05:19:46] x方向算符进度: 86/100, 当前算符: D_(0, 1) * D_(74, 95), 耗时: 10.541s
[2025-07-24 05:19:56] x方向算符进度: 87/100, 当前算符: D_(0, 1) * D_(76, 77), 耗时: 10.601s
[2025-07-24 05:20:07] x方向算符进度: 88/100, 当前算符: D_(0, 1) * D_(77, 96), 耗时: 10.541s
[2025-07-24 05:20:17] x方向算符进度: 89/100, 当前算符: D_(0, 1) * D_(78, 79), 耗时: 10.600s
[2025-07-24 05:20:28] x方向算符进度: 90/100, 当前算符: D_(0, 1) * D_(78, 99), 耗时: 10.540s
[2025-07-24 05:20:39] x方向算符进度: 91/100, 当前算符: D_(0, 1) * D_(80, 81), 耗时: 10.603s
[2025-07-24 05:20:49] x方向算符进度: 92/100, 当前算符: D_(0, 1) * D_(82, 83), 耗时: 10.543s
[2025-07-24 05:21:00] x方向算符进度: 93/100, 当前算符: D_(0, 1) * D_(84, 85), 耗时: 10.599s
[2025-07-24 05:21:10] x方向算符进度: 94/100, 当前算符: D_(0, 1) * D_(86, 87), 耗时: 10.541s
[2025-07-24 05:21:21] x方向算符进度: 95/100, 当前算符: D_(0, 1) * D_(88, 89), 耗时: 10.540s
[2025-07-24 05:21:31] x方向算符进度: 96/100, 当前算符: D_(0, 1) * D_(90, 91), 耗时: 10.602s
[2025-07-24 05:21:42] x方向算符进度: 97/100, 当前算符: D_(0, 1) * D_(92, 93), 耗时: 10.540s
[2025-07-24 05:21:53] x方向算符进度: 98/100, 当前算符: D_(0, 1) * D_(94, 95), 耗时: 10.600s
[2025-07-24 05:22:03] x方向算符进度: 99/100, 当前算符: D_(0, 1) * D_(96, 97), 耗时: 10.542s
[2025-07-24 05:22:14] x方向算符进度: 100/100, 当前算符: D_(0, 1) * D_(98, 99), 耗时: 10.601s
[2025-07-24 05:22:14] x方向二聚体相关函数计算完成,耗时: 1065.84 秒
[2025-07-24 05:22:14] --------------------------------------------------------------------------------
[2025-07-24 05:22:14] 使用y-二聚体 (0, 3) (全局索引 100) 作为参考点, 计算y方向连通二聚体相关函数...
[2025-07-24 05:22:20] y方向算符进度: 1/100, 当前算符: D_(0, 3) * D_(0, 3), 耗时: 6.282s
[2025-07-24 05:22:29] y方向算符进度: 2/100, 当前算符: D_(0, 3) * D_(0, 19), 耗时: 8.818s
[2025-07-24 05:22:39] y方向算符进度: 3/100, 当前算符: D_(0, 3) * D_(1, 2), 耗时: 10.626s
[2025-07-24 05:22:50] y方向算符进度: 4/100, 当前算符: D_(0, 3) * D_(1, 18), 耗时: 10.561s
[2025-07-24 05:23:01] y方向算符进度: 5/100, 当前算符: D_(0, 3) * D_(2, 5), 耗时: 10.561s
[2025-07-24 05:23:09] y方向算符进度: 6/100, 当前算符: D_(0, 3) * D_(3, 4), 耗时: 8.815s
[2025-07-24 05:23:20] y方向算符进度: 7/100, 当前算符: D_(0, 3) * D_(4, 7), 耗时: 10.562s
[2025-07-24 05:23:31] y方向算符进度: 8/100, 当前算符: D_(0, 3) * D_(5, 6), 耗时: 10.630s
[2025-07-24 05:23:41] y方向算符进度: 9/100, 当前算符: D_(0, 3) * D_(6, 9), 耗时: 10.563s
[2025-07-24 05:23:52] y方向算符进度: 10/100, 当前算符: D_(0, 3) * D_(7, 8), 耗时: 10.630s
[2025-07-24 05:24:02] y方向算符进度: 11/100, 当前算符: D_(0, 3) * D_(8, 11), 耗时: 10.562s
[2025-07-24 05:24:13] y方向算符进度: 12/100, 当前算符: D_(0, 3) * D_(9, 10), 耗时: 10.627s
[2025-07-24 05:24:24] y方向算符进度: 13/100, 当前算符: D_(0, 3) * D_(10, 13), 耗时: 10.561s
[2025-07-24 05:24:34] y方向算符进度: 14/100, 当前算符: D_(0, 3) * D_(11, 12), 耗时: 10.628s
[2025-07-24 05:24:45] y方向算符进度: 15/100, 当前算符: D_(0, 3) * D_(12, 15), 耗时: 10.567s
[2025-07-24 05:24:55] y方向算符进度: 16/100, 当前算符: D_(0, 3) * D_(13, 14), 耗时: 10.625s
[2025-07-24 05:25:06] y方向算符进度: 17/100, 当前算符: D_(0, 3) * D_(14, 17), 耗时: 10.567s
[2025-07-24 05:25:17] y方向算符进度: 18/100, 当前算符: D_(0, 3) * D_(15, 16), 耗时: 10.568s
[2025-07-24 05:25:27] y方向算符进度: 19/100, 当前算符: D_(0, 3) * D_(16, 19), 耗时: 10.634s
[2025-07-24 05:25:38] y方向算符进度: 20/100, 当前算符: D_(0, 3) * D_(17, 18), 耗时: 10.562s
[2025-07-24 05:25:48] y方向算符进度: 21/100, 当前算符: D_(0, 3) * D_(20, 23), 耗时: 10.627s
[2025-07-24 05:25:59] y方向算符进度: 22/100, 当前算符: D_(0, 3) * D_(20, 39), 耗时: 10.562s
[2025-07-24 05:26:10] y方向算符进度: 23/100, 当前算符: D_(0, 3) * D_(21, 22), 耗时: 10.630s
[2025-07-24 05:26:20] y方向算符进度: 24/100, 当前算符: D_(0, 3) * D_(21, 38), 耗时: 10.563s
[2025-07-24 05:26:31] y方向算符进度: 25/100, 当前算符: D_(0, 3) * D_(22, 25), 耗时: 10.626s
[2025-07-24 05:26:41] y方向算符进度: 26/100, 当前算符: D_(0, 3) * D_(23, 24), 耗时: 10.565s
[2025-07-24 05:26:52] y方向算符进度: 27/100, 当前算符: D_(0, 3) * D_(24, 27), 耗时: 10.633s
[2025-07-24 05:27:02] y方向算符进度: 28/100, 当前算符: D_(0, 3) * D_(25, 26), 耗时: 10.562s
[2025-07-24 05:27:13] y方向算符进度: 29/100, 当前算符: D_(0, 3) * D_(26, 29), 耗时: 10.637s
[2025-07-24 05:27:24] y方向算符进度: 30/100, 当前算符: D_(0, 3) * D_(27, 28), 耗时: 10.625s
[2025-07-24 05:27:34] y方向算符进度: 31/100, 当前算符: D_(0, 3) * D_(28, 31), 耗时: 10.565s
[2025-07-24 05:27:45] y方向算符进度: 32/100, 当前算符: D_(0, 3) * D_(29, 30), 耗时: 10.629s
[2025-07-24 05:27:56] y方向算符进度: 33/100, 当前算符: D_(0, 3) * D_(30, 33), 耗时: 10.564s
[2025-07-24 05:28:06] y方向算符进度: 34/100, 当前算符: D_(0, 3) * D_(31, 32), 耗时: 10.564s
[2025-07-24 05:28:17] y方向算符进度: 35/100, 当前算符: D_(0, 3) * D_(32, 35), 耗时: 10.625s
[2025-07-24 05:28:27] y方向算符进度: 36/100, 当前算符: D_(0, 3) * D_(33, 34), 耗时: 10.563s
[2025-07-24 05:28:38] y方向算符进度: 37/100, 当前算符: D_(0, 3) * D_(34, 37), 耗时: 10.629s
[2025-07-24 05:28:48] y方向算符进度: 38/100, 当前算符: D_(0, 3) * D_(35, 36), 耗时: 10.565s
[2025-07-24 05:28:59] y方向算符进度: 39/100, 当前算符: D_(0, 3) * D_(36, 39), 耗时: 10.633s
[2025-07-24 05:29:10] y方向算符进度: 40/100, 当前算符: D_(0, 3) * D_(37, 38), 耗时: 10.561s
[2025-07-24 05:29:20] y方向算符进度: 41/100, 当前算符: D_(0, 3) * D_(40, 43), 耗时: 10.632s
[2025-07-24 05:29:31] y方向算符进度: 42/100, 当前算符: D_(0, 3) * D_(40, 59), 耗时: 10.563s
[2025-07-24 05:29:41] y方向算符进度: 43/100, 当前算符: D_(0, 3) * D_(41, 42), 耗时: 10.630s
[2025-07-24 05:29:52] y方向算符进度: 44/100, 当前算符: D_(0, 3) * D_(41, 58), 耗时: 10.562s
[2025-07-24 05:30:03] y方向算符进度: 45/100, 当前算符: D_(0, 3) * D_(42, 45), 耗时: 10.625s
[2025-07-24 05:30:13] y方向算符进度: 46/100, 当前算符: D_(0, 3) * D_(43, 44), 耗时: 10.629s
[2025-07-24 05:30:24] y方向算符进度: 47/100, 当前算符: D_(0, 3) * D_(44, 47), 耗时: 10.563s
[2025-07-24 05:30:35] y方向算符进度: 48/100, 当前算符: D_(0, 3) * D_(45, 46), 耗时: 10.632s
[2025-07-24 05:30:45] y方向算符进度: 49/100, 当前算符: D_(0, 3) * D_(46, 49), 耗时: 10.564s
[2025-07-24 05:30:56] y方向算符进度: 50/100, 当前算符: D_(0, 3) * D_(47, 48), 耗时: 10.625s
[2025-07-24 05:31:06] y方向算符进度: 51/100, 当前算符: D_(0, 3) * D_(48, 51), 耗时: 10.628s
[2025-07-24 05:31:17] y方向算符进度: 52/100, 当前算符: D_(0, 3) * D_(49, 50), 耗时: 10.564s
[2025-07-24 05:31:28] y方向算符进度: 53/100, 当前算符: D_(0, 3) * D_(50, 53), 耗时: 10.625s
[2025-07-24 05:31:38] y方向算符进度: 54/100, 当前算符: D_(0, 3) * D_(51, 52), 耗时: 10.564s
[2025-07-24 05:31:49] y方向算符进度: 55/100, 当前算符: D_(0, 3) * D_(52, 55), 耗时: 10.632s
[2025-07-24 05:31:59] y方向算符进度: 56/100, 当前算符: D_(0, 3) * D_(53, 54), 耗时: 10.570s
[2025-07-24 05:32:10] y方向算符进度: 57/100, 当前算符: D_(0, 3) * D_(54, 57), 耗时: 10.627s
[2025-07-24 05:32:20] y方向算符进度: 58/100, 当前算符: D_(0, 3) * D_(55, 56), 耗时: 10.562s
[2025-07-24 05:32:31] y方向算符进度: 59/100, 当前算符: D_(0, 3) * D_(56, 59), 耗时: 10.637s
[2025-07-24 05:32:42] y方向算符进度: 60/100, 当前算符: D_(0, 3) * D_(57, 58), 耗时: 10.564s
[2025-07-24 05:32:52] y方向算符进度: 61/100, 当前算符: D_(0, 3) * D_(60, 63), 耗时: 10.629s
[2025-07-24 05:33:03] y方向算符进度: 62/100, 当前算符: D_(0, 3) * D_(60, 79), 耗时: 10.631s
[2025-07-24 05:33:14] y方向算符进度: 63/100, 当前算符: D_(0, 3) * D_(61, 62), 耗时: 10.562s
[2025-07-24 05:33:24] y方向算符进度: 64/100, 当前算符: D_(0, 3) * D_(61, 78), 耗时: 10.633s
[2025-07-24 05:33:35] y方向算符进度: 65/100, 当前算符: D_(0, 3) * D_(62, 65), 耗时: 10.563s
[2025-07-24 05:33:45] y方向算符进度: 66/100, 当前算符: D_(0, 3) * D_(63, 64), 耗时: 10.628s
[2025-07-24 05:33:56] y方向算符进度: 67/100, 当前算符: D_(0, 3) * D_(64, 67), 耗时: 10.568s
[2025-07-24 05:34:06] y方向算符进度: 68/100, 当前算符: D_(0, 3) * D_(65, 66), 耗时: 10.562s
[2025-07-24 05:34:17] y方向算符进度: 69/100, 当前算符: D_(0, 3) * D_(66, 69), 耗时: 10.627s
[2025-07-24 05:34:28] y方向算符进度: 70/100, 当前算符: D_(0, 3) * D_(67, 68), 耗时: 10.562s
[2025-07-24 05:34:38] y方向算符进度: 71/100, 当前算符: D_(0, 3) * D_(68, 71), 耗时: 10.626s
[2025-07-24 05:34:49] y方向算符进度: 72/100, 当前算符: D_(0, 3) * D_(69, 70), 耗时: 10.563s
[2025-07-24 05:34:59] y方向算符进度: 73/100, 当前算符: D_(0, 3) * D_(70, 73), 耗时: 10.629s
[2025-07-24 05:35:10] y方向算符进度: 74/100, 当前算符: D_(0, 3) * D_(71, 72), 耗时: 10.564s
[2025-07-24 05:35:21] y方向算符进度: 75/100, 当前算符: D_(0, 3) * D_(72, 75), 耗时: 10.627s
[2025-07-24 05:35:31] y方向算符进度: 76/100, 当前算符: D_(0, 3) * D_(73, 74), 耗时: 10.568s
[2025-07-24 05:35:42] y方向算符进度: 77/100, 当前算符: D_(0, 3) * D_(74, 77), 耗时: 10.627s
[2025-07-24 05:35:53] y方向算符进度: 78/100, 当前算符: D_(0, 3) * D_(75, 76), 耗时: 10.627s
[2025-07-24 05:36:03] y方向算符进度: 79/100, 当前算符: D_(0, 3) * D_(76, 79), 耗时: 10.563s
[2025-07-24 05:36:14] y方向算符进度: 80/100, 当前算符: D_(0, 3) * D_(77, 78), 耗时: 10.626s
[2025-07-24 05:36:24] y方向算符进度: 81/100, 当前算符: D_(0, 3) * D_(80, 83), 耗时: 10.563s
[2025-07-24 05:36:35] y方向算符进度: 82/100, 当前算符: D_(0, 3) * D_(80, 99), 耗时: 10.629s
[2025-07-24 05:36:45] y方向算符进度: 83/100, 当前算符: D_(0, 3) * D_(81, 82), 耗时: 10.562s
[2025-07-24 05:36:56] y方向算符进度: 84/100, 当前算符: D_(0, 3) * D_(81, 98), 耗时: 10.628s
[2025-07-24 05:37:07] y方向算符进度: 85/100, 当前算符: D_(0, 3) * D_(82, 85), 耗时: 10.562s
[2025-07-24 05:37:17] y方向算符进度: 86/100, 当前算符: D_(0, 3) * D_(83, 84), 耗时: 10.628s
[2025-07-24 05:37:28] y方向算符进度: 87/100, 当前算符: D_(0, 3) * D_(84, 87), 耗时: 10.562s
[2025-07-24 05:37:38] y方向算符进度: 88/100, 当前算符: D_(0, 3) * D_(85, 86), 耗时: 10.626s
[2025-07-24 05:37:49] y方向算符进度: 89/100, 当前算符: D_(0, 3) * D_(86, 89), 耗时: 10.576s
[2025-07-24 05:38:00] y方向算符进度: 90/100, 当前算符: D_(0, 3) * D_(87, 88), 耗时: 10.638s
[2025-07-24 05:38:10] y方向算符进度: 91/100, 当前算符: D_(0, 3) * D_(88, 91), 耗时: 10.563s
[2025-07-24 05:38:21] y方向算符进度: 92/100, 当前算符: D_(0, 3) * D_(89, 90), 耗时: 10.630s
[2025-07-24 05:38:31] y方向算符进度: 93/100, 当前算符: D_(0, 3) * D_(90, 93), 耗时: 10.562s
[2025-07-24 05:38:42] y方向算符进度: 94/100, 当前算符: D_(0, 3) * D_(91, 92), 耗时: 10.569s
[2025-07-24 05:38:53] y方向算符进度: 95/100, 当前算符: D_(0, 3) * D_(92, 95), 耗时: 10.627s
[2025-07-24 05:39:03] y方向算符进度: 96/100, 当前算符: D_(0, 3) * D_(93, 94), 耗时: 10.563s
[2025-07-24 05:39:14] y方向算符进度: 97/100, 当前算符: D_(0, 3) * D_(94, 97), 耗时: 10.631s
[2025-07-24 05:39:24] y方向算符进度: 98/100, 当前算符: D_(0, 3) * D_(95, 96), 耗时: 10.563s
[2025-07-24 05:39:35] y方向算符进度: 99/100, 当前算符: D_(0, 3) * D_(96, 99), 耗时: 10.630s
[2025-07-24 05:39:46] y方向算符进度: 100/100, 当前算符: D_(0, 3) * D_(97, 98), 耗时: 10.563s
[2025-07-24 05:39:46] y方向二聚体相关函数计算完成,耗时: 1051.84 秒
[2025-07-24 05:39:46] 计算傅里叶变换...
[2025-07-24 05:39:47] 二聚体结构因子计算完成
[2025-07-24 05:39:47] 二聚体相关函数平均误差: 0.000556
[2025-07-24 05:39:47] 恢复原始样本数: 4096
[2025-07-24 05:39:47] ================================================================================
[2025-07-24 05:39:47] 所有分析完成
