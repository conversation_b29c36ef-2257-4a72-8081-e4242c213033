[2025-07-24 02:21:22] ================================================================================
[2025-07-24 02:21:22] 加载量子态: L=5, J2=0.00, J1=0.06
[2025-07-24 02:21:22] 设置样本数为: 1048576
[2025-07-24 02:21:22] 开始生成共享样本集...
[2025-07-24 02:24:42] 样本生成完成,耗时: 199.685 秒
[2025-07-24 02:24:42] ================================================================================
[2025-07-24 02:24:42] 开始计算自旋结构因子...
[2025-07-24 02:24:42] 初始化操作符缓存...
[2025-07-24 02:24:42] 预构建所有自旋相关操作符...
[2025-07-24 02:24:42] 开始计算自旋相关函数...
[2025-07-24 02:24:52] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 10.402s
[2025-07-24 02:25:04] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 12.126s
[2025-07-24 02:25:11] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.313s
[2025-07-24 02:25:17] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.314s
[2025-07-24 02:25:23] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.284s
[2025-07-24 02:25:29] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.314s
[2025-07-24 02:25:36] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.314s
[2025-07-24 02:25:42] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.284s
[2025-07-24 02:25:48] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.315s
[2025-07-24 02:25:55] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.285s
[2025-07-24 02:26:01] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.312s
[2025-07-24 02:26:07] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.285s
[2025-07-24 02:26:14] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.314s
[2025-07-24 02:26:20] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.312s
[2025-07-24 02:26:26] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.314s
[2025-07-24 02:26:33] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.284s
[2025-07-24 02:26:39] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.313s
[2025-07-24 02:26:45] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.284s
[2025-07-24 02:26:51] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.311s
[2025-07-24 02:26:58] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.313s
[2025-07-24 02:27:04] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.283s
[2025-07-24 02:27:10] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.284s
[2025-07-24 02:27:17] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.314s
[2025-07-24 02:27:23] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.312s
[2025-07-24 02:27:29] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.313s
[2025-07-24 02:27:36] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.313s
[2025-07-24 02:27:42] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.283s
[2025-07-24 02:27:48] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.312s
[2025-07-24 02:27:54] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.283s
[2025-07-24 02:28:01] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.312s
[2025-07-24 02:28:07] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.313s
[2025-07-24 02:28:13] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.313s
[2025-07-24 02:28:20] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.282s
[2025-07-24 02:28:26] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.282s
[2025-07-24 02:28:32] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.310s
[2025-07-24 02:28:39] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.283s
[2025-07-24 02:28:45] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.313s
[2025-07-24 02:28:51] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.313s
[2025-07-24 02:28:57] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.312s
[2025-07-24 02:29:04] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.313s
[2025-07-24 02:29:10] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.310s
[2025-07-24 02:29:16] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.311s
[2025-07-24 02:29:23] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.312s
[2025-07-24 02:29:29] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.311s
[2025-07-24 02:29:35] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.312s
[2025-07-24 02:29:42] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.313s
[2025-07-24 02:29:48] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.312s
[2025-07-24 02:29:54] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.312s
[2025-07-24 02:30:01] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.311s
[2025-07-24 02:30:07] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.282s
[2025-07-24 02:30:13] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.313s
[2025-07-24 02:30:19] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.283s
[2025-07-24 02:30:26] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.312s
[2025-07-24 02:30:32] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.312s
[2025-07-24 02:30:38] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.313s
[2025-07-24 02:30:45] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.283s
[2025-07-24 02:30:51] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.312s
[2025-07-24 02:30:57] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.394s
[2025-07-24 02:31:04] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.312s
[2025-07-24 02:31:10] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.312s
[2025-07-24 02:31:16] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.283s
[2025-07-24 02:31:23] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.283s
[2025-07-24 02:31:29] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.313s
[2025-07-24 02:31:35] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.312s
[2025-07-24 02:31:42] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.313s
[2025-07-24 02:31:48] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.313s
[2025-07-24 02:31:54] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.284s
[2025-07-24 02:32:00] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.322s
[2025-07-24 02:32:07] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.312s
[2025-07-24 02:32:13] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.312s
[2025-07-24 02:32:19] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.314s
[2025-07-24 02:32:26] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.283s
[2025-07-24 02:32:32] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.313s
[2025-07-24 02:32:38] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.314s
[2025-07-24 02:32:45] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.312s
[2025-07-24 02:32:51] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.313s
[2025-07-24 02:32:57] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.284s
[2025-07-24 02:33:04] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.284s
[2025-07-24 02:33:10] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.314s
[2025-07-24 02:33:16] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.313s
[2025-07-24 02:33:22] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.314s
[2025-07-24 02:33:29] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.313s
[2025-07-24 02:33:35] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.284s
[2025-07-24 02:33:41] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.313s
[2025-07-24 02:33:48] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.312s
[2025-07-24 02:33:54] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.313s
[2025-07-24 02:34:00] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.313s
[2025-07-24 02:34:07] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.314s
[2025-07-24 02:34:13] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.286s
[2025-07-24 02:34:19] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.312s
[2025-07-24 02:34:26] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.313s
[2025-07-24 02:34:32] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.312s
[2025-07-24 02:34:38] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.313s
[2025-07-24 02:34:44] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.313s
[2025-07-24 02:34:51] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.282s
[2025-07-24 02:34:57] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.312s
[2025-07-24 02:35:03] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.312s
[2025-07-24 02:35:10] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.311s
[2025-07-24 02:35:16] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.312s
[2025-07-24 02:35:22] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.284s
[2025-07-24 02:35:22] 自旋相关函数计算完成,总耗时 640.59 秒
[2025-07-24 02:35:22] 计算傅里叶变换...
[2025-07-24 02:35:23] 自旋结构因子计算完成
[2025-07-24 02:35:24] 自旋相关函数平均误差: 0.000745
[2025-07-24 02:35:24] ================================================================================
[2025-07-24 02:35:24] 开始计算二聚体结构因子...
[2025-07-24 02:35:24] 识别x和y方向的二聚体...
[2025-07-24 02:35:24] 找到 100 个x方向二聚体和 100 个y方向二聚体
[2025-07-24 02:35:24] 预计算二聚体操作符...
[2025-07-24 02:35:25] 使用x-二聚体 (0, 1) (全局索引 0) 作为参考点, 计算x方向连通二聚体相关函数...
[2025-07-24 02:35:32] x方向算符进度: 1/100, 当前算符: D_(0, 1) * D_(0, 1), 耗时: 6.367s
[2025-07-24 02:35:50] x方向算符进度: 2/100, 当前算符: D_(0, 1) * D_(0, 81), 耗时: 18.922s
[2025-07-24 02:35:59] x方向算符进度: 3/100, 当前算符: D_(0, 1) * D_(1, 20), 耗时: 8.839s
[2025-07-24 02:36:16] x方向算符进度: 4/100, 当前算符: D_(0, 1) * D_(2, 3), 耗时: 16.343s
[2025-07-24 02:36:26] x方向算符进度: 5/100, 当前算符: D_(0, 1) * D_(2, 23), 耗时: 10.567s
[2025-07-24 02:36:37] x方向算符进度: 6/100, 当前算符: D_(0, 1) * D_(3, 82), 耗时: 10.625s
[2025-07-24 02:36:47] x方向算符进度: 7/100, 当前算符: D_(0, 1) * D_(4, 5), 耗时: 10.566s
[2025-07-24 02:36:58] x方向算符进度: 8/100, 当前算符: D_(0, 1) * D_(4, 85), 耗时: 10.624s
[2025-07-24 02:37:09] x方向算符进度: 9/100, 当前算符: D_(0, 1) * D_(5, 24), 耗时: 10.624s
[2025-07-24 02:37:19] x方向算符进度: 10/100, 当前算符: D_(0, 1) * D_(6, 7), 耗时: 10.563s
[2025-07-24 02:37:30] x方向算符进度: 11/100, 当前算符: D_(0, 1) * D_(6, 27), 耗时: 10.619s
[2025-07-24 02:37:40] x方向算符进度: 12/100, 当前算符: D_(0, 1) * D_(7, 86), 耗时: 10.623s
[2025-07-24 02:37:51] x方向算符进度: 13/100, 当前算符: D_(0, 1) * D_(8, 9), 耗时: 10.604s
[2025-07-24 02:38:02] x方向算符进度: 14/100, 当前算符: D_(0, 1) * D_(8, 89), 耗时: 10.628s
[2025-07-24 02:38:12] x方向算符进度: 15/100, 当前算符: D_(0, 1) * D_(9, 28), 耗时: 10.562s
[2025-07-24 02:38:23] x方向算符进度: 16/100, 当前算符: D_(0, 1) * D_(10, 11), 耗时: 10.624s
[2025-07-24 02:38:33] x方向算符进度: 17/100, 当前算符: D_(0, 1) * D_(10, 31), 耗时: 10.564s
[2025-07-24 02:38:44] x方向算符进度: 18/100, 当前算符: D_(0, 1) * D_(11, 90), 耗时: 10.603s
[2025-07-24 02:38:55] x方向算符进度: 19/100, 当前算符: D_(0, 1) * D_(12, 13), 耗时: 10.624s
[2025-07-24 02:39:05] x方向算符进度: 20/100, 当前算符: D_(0, 1) * D_(12, 93), 耗时: 10.604s
[2025-07-24 02:39:16] x方向算符进度: 21/100, 当前算符: D_(0, 1) * D_(13, 32), 耗时: 10.623s
[2025-07-24 02:39:26] x方向算符进度: 22/100, 当前算符: D_(0, 1) * D_(14, 15), 耗时: 10.562s
[2025-07-24 02:39:37] x方向算符进度: 23/100, 当前算符: D_(0, 1) * D_(14, 35), 耗时: 10.624s
[2025-07-24 02:39:48] x方向算符进度: 24/100, 当前算符: D_(0, 1) * D_(15, 94), 耗时: 10.602s
[2025-07-24 02:39:58] x方向算符进度: 25/100, 当前算符: D_(0, 1) * D_(16, 17), 耗时: 10.621s
[2025-07-24 02:40:09] x方向算符进度: 26/100, 当前算符: D_(0, 1) * D_(16, 97), 耗时: 10.624s
[2025-07-24 02:40:20] x方向算符进度: 27/100, 当前算符: D_(0, 1) * D_(17, 36), 耗时: 10.567s
[2025-07-24 02:40:30] x方向算符进度: 28/100, 当前算符: D_(0, 1) * D_(18, 19), 耗时: 10.623s
[2025-07-24 02:40:41] x方向算符进度: 29/100, 当前算符: D_(0, 1) * D_(18, 39), 耗时: 10.603s
[2025-07-24 02:40:51] x方向算符进度: 30/100, 当前算符: D_(0, 1) * D_(19, 98), 耗时: 10.621s
[2025-07-24 02:41:02] x方向算符进度: 31/100, 当前算符: D_(0, 1) * D_(20, 21), 耗时: 10.624s
[2025-07-24 02:41:13] x方向算符进度: 32/100, 当前算符: D_(0, 1) * D_(21, 40), 耗时: 10.569s
[2025-07-24 02:41:23] x方向算符进度: 33/100, 当前算符: D_(0, 1) * D_(22, 23), 耗时: 10.623s
[2025-07-24 02:41:34] x方向算符进度: 34/100, 当前算符: D_(0, 1) * D_(22, 43), 耗时: 10.565s
[2025-07-24 02:41:44] x方向算符进度: 35/100, 当前算符: D_(0, 1) * D_(24, 25), 耗时: 10.624s
[2025-07-24 02:41:55] x方向算符进度: 36/100, 当前算符: D_(0, 1) * D_(25, 44), 耗时: 10.603s
[2025-07-24 02:42:06] x方向算符进度: 37/100, 当前算符: D_(0, 1) * D_(26, 27), 耗时: 10.611s
[2025-07-24 02:42:16] x方向算符进度: 38/100, 当前算符: D_(0, 1) * D_(26, 47), 耗时: 10.563s
[2025-07-24 02:42:27] x方向算符进度: 39/100, 当前算符: D_(0, 1) * D_(28, 29), 耗时: 10.624s
[2025-07-24 02:42:37] x方向算符进度: 40/100, 当前算符: D_(0, 1) * D_(29, 48), 耗时: 10.603s
[2025-07-24 02:42:48] x方向算符进度: 41/100, 当前算符: D_(0, 1) * D_(30, 31), 耗时: 10.619s
[2025-07-24 02:42:59] x方向算符进度: 42/100, 当前算符: D_(0, 1) * D_(30, 51), 耗时: 10.603s
[2025-07-24 02:43:09] x方向算符进度: 43/100, 当前算符: D_(0, 1) * D_(32, 33), 耗时: 10.601s
[2025-07-24 02:43:20] x方向算符进度: 44/100, 当前算符: D_(0, 1) * D_(33, 52), 耗时: 10.625s
[2025-07-24 02:43:30] x方向算符进度: 45/100, 当前算符: D_(0, 1) * D_(34, 35), 耗时: 10.561s
[2025-07-24 02:43:41] x方向算符进度: 46/100, 当前算符: D_(0, 1) * D_(34, 55), 耗时: 10.621s
[2025-07-24 02:43:52] x方向算符进度: 47/100, 当前算符: D_(0, 1) * D_(36, 37), 耗时: 10.625s
[2025-07-24 02:44:02] x方向算符进度: 48/100, 当前算符: D_(0, 1) * D_(37, 56), 耗时: 10.604s
[2025-07-24 02:44:13] x方向算符进度: 49/100, 当前算符: D_(0, 1) * D_(38, 39), 耗时: 10.625s
[2025-07-24 02:44:23] x方向算符进度: 50/100, 当前算符: D_(0, 1) * D_(38, 59), 耗时: 10.565s
[2025-07-24 02:44:34] x方向算符进度: 51/100, 当前算符: D_(0, 1) * D_(40, 41), 耗时: 10.624s
[2025-07-24 02:44:45] x方向算符进度: 52/100, 当前算符: D_(0, 1) * D_(41, 60), 耗时: 10.602s
[2025-07-24 02:44:55] x方向算符进度: 53/100, 当前算符: D_(0, 1) * D_(42, 43), 耗时: 10.624s
[2025-07-24 02:45:06] x方向算符进度: 54/100, 当前算符: D_(0, 1) * D_(42, 63), 耗时: 10.602s
[2025-07-24 02:45:17] x方向算符进度: 55/100, 当前算符: D_(0, 1) * D_(44, 45), 耗时: 10.621s
[2025-07-24 02:45:27] x方向算符进度: 56/100, 当前算符: D_(0, 1) * D_(45, 64), 耗时: 10.567s
[2025-07-24 02:45:38] x方向算符进度: 57/100, 当前算符: D_(0, 1) * D_(46, 47), 耗时: 10.623s
[2025-07-24 02:45:48] x方向算符进度: 58/100, 当前算符: D_(0, 1) * D_(46, 67), 耗时: 10.600s
[2025-07-24 02:45:59] x方向算符进度: 59/100, 当前算符: D_(0, 1) * D_(48, 49), 耗时: 10.617s
[2025-07-24 02:46:09] x方向算符进度: 60/100, 当前算符: D_(0, 1) * D_(49, 68), 耗时: 10.562s
[2025-07-24 02:46:20] x方向算符进度: 61/100, 当前算符: D_(0, 1) * D_(50, 51), 耗时: 10.623s
[2025-07-24 02:46:31] x方向算符进度: 62/100, 当前算符: D_(0, 1) * D_(50, 71), 耗时: 10.600s
[2025-07-24 02:46:41] x方向算符进度: 63/100, 当前算符: D_(0, 1) * D_(52, 53), 耗时: 10.600s
[2025-07-24 02:46:52] x方向算符进度: 64/100, 当前算符: D_(0, 1) * D_(53, 72), 耗时: 10.616s
[2025-07-24 02:47:02] x方向算符进度: 65/100, 当前算符: D_(0, 1) * D_(54, 55), 耗时: 10.561s
[2025-07-24 02:47:13] x方向算符进度: 66/100, 当前算符: D_(0, 1) * D_(54, 75), 耗时: 10.623s
[2025-07-24 02:47:24] x方向算符进度: 67/100, 当前算符: D_(0, 1) * D_(56, 57), 耗时: 10.564s
[2025-07-24 02:47:34] x方向算符进度: 68/100, 当前算符: D_(0, 1) * D_(57, 76), 耗时: 10.624s
[2025-07-24 02:47:45] x方向算符进度: 69/100, 当前算符: D_(0, 1) * D_(58, 59), 耗时: 10.622s
[2025-07-24 02:47:55] x方向算符进度: 70/100, 当前算符: D_(0, 1) * D_(58, 79), 耗时: 10.563s
[2025-07-24 02:48:06] x方向算符进度: 71/100, 当前算符: D_(0, 1) * D_(60, 61), 耗时: 10.623s
[2025-07-24 02:48:17] x方向算符进度: 72/100, 当前算符: D_(0, 1) * D_(61, 80), 耗时: 10.561s
[2025-07-24 02:48:27] x方向算符进度: 73/100, 当前算符: D_(0, 1) * D_(62, 63), 耗时: 10.618s
[2025-07-24 02:48:38] x方向算符进度: 74/100, 当前算符: D_(0, 1) * D_(62, 83), 耗时: 10.601s
[2025-07-24 02:48:49] x方向算符进度: 75/100, 当前算符: D_(0, 1) * D_(64, 65), 耗时: 10.624s
[2025-07-24 02:48:59] x方向算符进度: 76/100, 当前算符: D_(0, 1) * D_(65, 84), 耗时: 10.564s
[2025-07-24 02:49:10] x方向算符进度: 77/100, 当前算符: D_(0, 1) * D_(66, 67), 耗时: 10.624s
[2025-07-24 02:49:20] x方向算符进度: 78/100, 当前算符: D_(0, 1) * D_(66, 87), 耗时: 10.566s
[2025-07-24 02:49:31] x方向算符进度: 79/100, 当前算符: D_(0, 1) * D_(68, 69), 耗时: 10.565s
[2025-07-24 02:49:41] x方向算符进度: 80/100, 当前算符: D_(0, 1) * D_(69, 88), 耗时: 10.624s
[2025-07-24 02:49:52] x方向算符进度: 81/100, 当前算符: D_(0, 1) * D_(70, 71), 耗时: 10.601s
[2025-07-24 02:50:03] x方向算符进度: 82/100, 当前算符: D_(0, 1) * D_(70, 91), 耗时: 10.624s
[2025-07-24 02:50:13] x方向算符进度: 83/100, 当前算符: D_(0, 1) * D_(72, 73), 耗时: 10.564s
[2025-07-24 02:50:24] x方向算符进度: 84/100, 当前算符: D_(0, 1) * D_(73, 92), 耗时: 10.611s
[2025-07-24 02:50:34] x方向算符进度: 85/100, 当前算符: D_(0, 1) * D_(74, 75), 耗时: 10.603s
[2025-07-24 02:50:45] x方向算符进度: 86/100, 当前算符: D_(0, 1) * D_(74, 95), 耗时: 10.601s
[2025-07-24 02:50:56] x方向算符进度: 87/100, 当前算符: D_(0, 1) * D_(76, 77), 耗时: 10.623s
[2025-07-24 02:51:06] x方向算符进度: 88/100, 当前算符: D_(0, 1) * D_(77, 96), 耗时: 10.565s
[2025-07-24 02:51:17] x方向算符进度: 89/100, 当前算符: D_(0, 1) * D_(78, 79), 耗时: 10.625s
[2025-07-24 02:51:27] x方向算符进度: 90/100, 当前算符: D_(0, 1) * D_(78, 99), 耗时: 10.563s
[2025-07-24 02:51:38] x方向算符进度: 91/100, 当前算符: D_(0, 1) * D_(80, 81), 耗时: 10.623s
[2025-07-24 02:51:49] x方向算符进度: 92/100, 当前算符: D_(0, 1) * D_(82, 83), 耗时: 10.602s
[2025-07-24 02:51:59] x方向算符进度: 93/100, 当前算符: D_(0, 1) * D_(84, 85), 耗时: 10.624s
[2025-07-24 02:52:10] x方向算符进度: 94/100, 当前算符: D_(0, 1) * D_(86, 87), 耗时: 10.604s
[2025-07-24 02:52:20] x方向算符进度: 95/100, 当前算符: D_(0, 1) * D_(88, 89), 耗时: 10.561s
[2025-07-24 02:52:31] x方向算符进度: 96/100, 当前算符: D_(0, 1) * D_(90, 91), 耗时: 10.623s
[2025-07-24 02:52:42] x方向算符进度: 97/100, 当前算符: D_(0, 1) * D_(92, 93), 耗时: 10.562s
[2025-07-24 02:52:52] x方向算符进度: 98/100, 当前算符: D_(0, 1) * D_(94, 95), 耗时: 10.622s
[2025-07-24 02:53:03] x方向算符进度: 99/100, 当前算符: D_(0, 1) * D_(96, 97), 耗时: 10.602s
[2025-07-24 02:53:14] x方向算符进度: 100/100, 当前算符: D_(0, 1) * D_(98, 99), 耗时: 10.623s
[2025-07-24 02:53:14] x方向二聚体相关函数计算完成,耗时: 1068.35 秒
[2025-07-24 02:53:14] --------------------------------------------------------------------------------
[2025-07-24 02:53:14] 使用y-二聚体 (0, 3) (全局索引 100) 作为参考点, 计算y方向连通二聚体相关函数...
[2025-07-24 02:53:20] y方向算符进度: 1/100, 当前算符: D_(0, 3) * D_(0, 3), 耗时: 6.291s
[2025-07-24 02:53:29] y方向算符进度: 2/100, 当前算符: D_(0, 3) * D_(0, 19), 耗时: 8.832s
[2025-07-24 02:53:39] y方向算符进度: 3/100, 当前算符: D_(0, 3) * D_(1, 2), 耗时: 10.646s
[2025-07-24 02:53:50] y方向算符进度: 4/100, 当前算符: D_(0, 3) * D_(1, 18), 耗时: 10.582s
[2025-07-24 02:54:00] y方向算符进度: 5/100, 当前算符: D_(0, 3) * D_(2, 5), 耗时: 10.583s
[2025-07-24 02:54:09] y方向算符进度: 6/100, 当前算符: D_(0, 3) * D_(3, 4), 耗时: 8.825s
[2025-07-24 02:54:20] y方向算符进度: 7/100, 当前算符: D_(0, 3) * D_(4, 7), 耗时: 10.605s
[2025-07-24 02:54:31] y方向算符进度: 8/100, 当前算符: D_(0, 3) * D_(5, 6), 耗时: 10.647s
[2025-07-24 02:54:41] y方向算符进度: 9/100, 当前算符: D_(0, 3) * D_(6, 9), 耗时: 10.581s
[2025-07-24 02:54:52] y方向算符进度: 10/100, 当前算符: D_(0, 3) * D_(7, 8), 耗时: 10.647s
[2025-07-24 02:55:02] y方向算符进度: 11/100, 当前算符: D_(0, 3) * D_(8, 11), 耗时: 10.576s
[2025-07-24 02:55:13] y方向算符进度: 12/100, 当前算符: D_(0, 3) * D_(9, 10), 耗时: 10.646s
[2025-07-24 02:55:24] y方向算符进度: 13/100, 当前算符: D_(0, 3) * D_(10, 13), 耗时: 10.578s
[2025-07-24 02:55:34] y方向算符进度: 14/100, 当前算符: D_(0, 3) * D_(11, 12), 耗时: 10.625s
[2025-07-24 02:55:45] y方向算符进度: 15/100, 当前算符: D_(0, 3) * D_(12, 15), 耗时: 10.603s
[2025-07-24 02:55:55] y方向算符进度: 16/100, 当前算符: D_(0, 3) * D_(13, 14), 耗时: 10.636s
[2025-07-24 02:56:06] y方向算符进度: 17/100, 当前算符: D_(0, 3) * D_(14, 17), 耗时: 10.582s
[2025-07-24 02:56:17] y方向算符进度: 18/100, 当前算符: D_(0, 3) * D_(15, 16), 耗时: 10.570s
[2025-07-24 02:56:27] y方向算符进度: 19/100, 当前算符: D_(0, 3) * D_(16, 19), 耗时: 10.644s
[2025-07-24 02:56:38] y方向算符进度: 20/100, 当前算符: D_(0, 3) * D_(17, 18), 耗时: 10.602s
[2025-07-24 02:56:48] y方向算符进度: 21/100, 当前算符: D_(0, 3) * D_(20, 23), 耗时: 10.645s
[2025-07-24 02:56:59] y方向算符进度: 22/100, 当前算符: D_(0, 3) * D_(20, 39), 耗时: 10.578s
[2025-07-24 02:57:10] y方向算符进度: 23/100, 当前算符: D_(0, 3) * D_(21, 22), 耗时: 10.647s
[2025-07-24 02:57:20] y方向算符进度: 24/100, 当前算符: D_(0, 3) * D_(21, 38), 耗时: 10.607s
[2025-07-24 02:57:31] y方向算符进度: 25/100, 当前算符: D_(0, 3) * D_(22, 25), 耗时: 10.643s
[2025-07-24 02:57:42] y方向算符进度: 26/100, 当前算符: D_(0, 3) * D_(23, 24), 耗时: 10.605s
[2025-07-24 02:57:52] y方向算符进度: 27/100, 当前算符: D_(0, 3) * D_(24, 27), 耗时: 10.645s
[2025-07-24 02:58:03] y方向算符进度: 28/100, 当前算符: D_(0, 3) * D_(25, 26), 耗时: 10.604s
[2025-07-24 02:58:13] y方向算符进度: 29/100, 当前算符: D_(0, 3) * D_(26, 29), 耗时: 10.644s
[2025-07-24 02:58:24] y方向算符进度: 30/100, 当前算符: D_(0, 3) * D_(27, 28), 耗时: 10.635s
[2025-07-24 02:58:35] y方向算符进度: 31/100, 当前算符: D_(0, 3) * D_(28, 31), 耗时: 10.582s
[2025-07-24 02:58:45] y方向算符进度: 32/100, 当前算符: D_(0, 3) * D_(29, 30), 耗时: 10.646s
[2025-07-24 02:58:56] y方向算符进度: 33/100, 当前算符: D_(0, 3) * D_(30, 33), 耗时: 10.603s
[2025-07-24 02:59:07] y方向算符进度: 34/100, 当前算符: D_(0, 3) * D_(31, 32), 耗时: 10.605s
[2025-07-24 02:59:17] y方向算符进度: 35/100, 当前算符: D_(0, 3) * D_(32, 35), 耗时: 10.652s
[2025-07-24 02:59:28] y方向算符进度: 36/100, 当前算符: D_(0, 3) * D_(33, 34), 耗时: 10.575s
[2025-07-24 02:59:38] y方向算符进度: 37/100, 当前算符: D_(0, 3) * D_(34, 37), 耗时: 10.647s
[2025-07-24 02:59:49] y方向算符进度: 38/100, 当前算符: D_(0, 3) * D_(35, 36), 耗时: 10.603s
[2025-07-24 03:00:00] y方向算符进度: 39/100, 当前算符: D_(0, 3) * D_(36, 39), 耗时: 10.647s
[2025-07-24 03:00:10] y方向算符进度: 40/100, 当前算符: D_(0, 3) * D_(37, 38), 耗时: 10.578s
[2025-07-24 03:00:21] y方向算符进度: 41/100, 当前算符: D_(0, 3) * D_(40, 43), 耗时: 10.645s
[2025-07-24 03:00:31] y方向算符进度: 42/100, 当前算符: D_(0, 3) * D_(40, 59), 耗时: 10.581s
[2025-07-24 03:00:42] y方向算符进度: 43/100, 当前算符: D_(0, 3) * D_(41, 42), 耗时: 10.646s
[2025-07-24 03:00:53] y方向算符进度: 44/100, 当前算符: D_(0, 3) * D_(41, 58), 耗时: 10.603s
[2025-07-24 03:01:03] y方向算符进度: 45/100, 当前算符: D_(0, 3) * D_(42, 45), 耗时: 10.640s
[2025-07-24 03:01:14] y方向算符进度: 46/100, 当前算符: D_(0, 3) * D_(43, 44), 耗时: 10.647s
[2025-07-24 03:01:25] y方向算符进度: 47/100, 当前算符: D_(0, 3) * D_(44, 47), 耗时: 10.581s
[2025-07-24 03:01:35] y方向算符进度: 48/100, 当前算符: D_(0, 3) * D_(45, 46), 耗时: 10.646s
[2025-07-24 03:01:46] y方向算符进度: 49/100, 当前算符: D_(0, 3) * D_(46, 49), 耗时: 10.574s
[2025-07-24 03:01:56] y方向算符进度: 50/100, 当前算符: D_(0, 3) * D_(47, 48), 耗时: 10.640s
[2025-07-24 03:02:07] y方向算符进度: 51/100, 当前算符: D_(0, 3) * D_(48, 51), 耗时: 10.645s
[2025-07-24 03:02:18] y方向算符进度: 52/100, 当前算符: D_(0, 3) * D_(49, 50), 耗时: 10.603s
[2025-07-24 03:02:28] y方向算符进度: 53/100, 当前算符: D_(0, 3) * D_(50, 53), 耗时: 10.641s
[2025-07-24 03:02:39] y方向算符进度: 54/100, 当前算符: D_(0, 3) * D_(51, 52), 耗时: 10.581s
[2025-07-24 03:02:50] y方向算符进度: 55/100, 当前算符: D_(0, 3) * D_(52, 55), 耗时: 10.647s
[2025-07-24 03:03:00] y方向算符进度: 56/100, 当前算符: D_(0, 3) * D_(53, 54), 耗时: 10.603s
[2025-07-24 03:03:11] y方向算符进度: 57/100, 当前算符: D_(0, 3) * D_(54, 57), 耗时: 10.644s
[2025-07-24 03:03:21] y方向算符进度: 58/100, 当前算符: D_(0, 3) * D_(55, 56), 耗时: 10.602s
[2025-07-24 03:03:32] y方向算符进度: 59/100, 当前算符: D_(0, 3) * D_(56, 59), 耗时: 10.644s
[2025-07-24 03:03:43] y方向算符进度: 60/100, 当前算符: D_(0, 3) * D_(57, 58), 耗时: 10.582s
[2025-07-24 03:03:53] y方向算符进度: 61/100, 当前算符: D_(0, 3) * D_(60, 63), 耗时: 10.646s
[2025-07-24 03:04:04] y方向算符进度: 62/100, 当前算符: D_(0, 3) * D_(60, 79), 耗时: 10.647s
[2025-07-24 03:04:15] y方向算符进度: 63/100, 当前算符: D_(0, 3) * D_(61, 62), 耗时: 10.602s
[2025-07-24 03:04:25] y方向算符进度: 64/100, 当前算符: D_(0, 3) * D_(61, 78), 耗时: 10.652s
[2025-07-24 03:04:36] y方向算符进度: 65/100, 当前算符: D_(0, 3) * D_(62, 65), 耗时: 10.580s
[2025-07-24 03:04:46] y方向算符进度: 66/100, 当前算符: D_(0, 3) * D_(63, 64), 耗时: 10.625s
[2025-07-24 03:04:57] y方向算符进度: 67/100, 当前算符: D_(0, 3) * D_(64, 67), 耗时: 10.602s
[2025-07-24 03:05:08] y方向算符进度: 68/100, 当前算符: D_(0, 3) * D_(65, 66), 耗时: 10.602s
[2025-07-24 03:05:18] y方向算符进度: 69/100, 当前算符: D_(0, 3) * D_(66, 69), 耗时: 10.643s
[2025-07-24 03:05:29] y方向算符进度: 70/100, 当前算符: D_(0, 3) * D_(67, 68), 耗时: 10.581s
[2025-07-24 03:05:39] y方向算符进度: 71/100, 当前算符: D_(0, 3) * D_(68, 71), 耗时: 10.646s
[2025-07-24 03:05:50] y方向算符进度: 72/100, 当前算符: D_(0, 3) * D_(69, 70), 耗时: 10.607s
[2025-07-24 03:06:01] y方向算符进度: 73/100, 当前算符: D_(0, 3) * D_(70, 73), 耗时: 10.647s
[2025-07-24 03:06:11] y方向算符进度: 74/100, 当前算符: D_(0, 3) * D_(71, 72), 耗时: 10.603s
[2025-07-24 03:06:22] y方向算符进度: 75/100, 当前算符: D_(0, 3) * D_(72, 75), 耗时: 10.640s
[2025-07-24 03:06:33] y方向算符进度: 76/100, 当前算符: D_(0, 3) * D_(73, 74), 耗时: 10.582s
[2025-07-24 03:06:43] y方向算符进度: 77/100, 当前算符: D_(0, 3) * D_(74, 77), 耗时: 10.639s
[2025-07-24 03:06:54] y方向算符进度: 78/100, 当前算符: D_(0, 3) * D_(75, 76), 耗时: 10.634s
[2025-07-24 03:07:04] y方向算符进度: 79/100, 当前算符: D_(0, 3) * D_(76, 79), 耗时: 10.603s
[2025-07-24 03:07:15] y方向算符进度: 80/100, 当前算符: D_(0, 3) * D_(77, 78), 耗时: 10.640s
[2025-07-24 03:07:26] y方向算符进度: 81/100, 当前算符: D_(0, 3) * D_(80, 83), 耗时: 10.586s
[2025-07-24 03:07:36] y方向算符进度: 82/100, 当前算符: D_(0, 3) * D_(80, 99), 耗时: 10.653s
[2025-07-24 03:07:47] y方向算符进度: 83/100, 当前算符: D_(0, 3) * D_(81, 82), 耗时: 10.602s
[2025-07-24 03:07:58] y方向算符进度: 84/100, 当前算符: D_(0, 3) * D_(81, 98), 耗时: 10.653s
[2025-07-24 03:08:08] y方向算符进度: 85/100, 当前算符: D_(0, 3) * D_(82, 85), 耗时: 10.584s
[2025-07-24 03:08:19] y方向算符进度: 86/100, 当前算符: D_(0, 3) * D_(83, 84), 耗时: 10.626s
[2025-07-24 03:08:29] y方向算符进度: 87/100, 当前算符: D_(0, 3) * D_(84, 87), 耗时: 10.581s
[2025-07-24 03:08:40] y方向算符进度: 88/100, 当前算符: D_(0, 3) * D_(85, 86), 耗时: 10.643s
[2025-07-24 03:08:51] y方向算符进度: 89/100, 当前算符: D_(0, 3) * D_(86, 89), 耗时: 10.606s
[2025-07-24 03:09:01] y方向算符进度: 90/100, 当前算符: D_(0, 3) * D_(87, 88), 耗时: 10.645s
[2025-07-24 03:09:12] y方向算符进度: 91/100, 当前算符: D_(0, 3) * D_(88, 91), 耗时: 10.603s
[2025-07-24 03:09:23] y方向算符进度: 92/100, 当前算符: D_(0, 3) * D_(89, 90), 耗时: 10.647s
[2025-07-24 03:09:33] y方向算符进度: 93/100, 当前算符: D_(0, 3) * D_(90, 93), 耗时: 10.580s
[2025-07-24 03:09:44] y方向算符进度: 94/100, 当前算符: D_(0, 3) * D_(91, 92), 耗时: 10.582s
[2025-07-24 03:09:54] y方向算符进度: 95/100, 当前算符: D_(0, 3) * D_(92, 95), 耗时: 10.645s
[2025-07-24 03:10:05] y方向算符进度: 96/100, 当前算符: D_(0, 3) * D_(93, 94), 耗时: 10.604s
[2025-07-24 03:10:16] y方向算符进度: 97/100, 当前算符: D_(0, 3) * D_(94, 97), 耗时: 10.647s
[2025-07-24 03:10:26] y方向算符进度: 98/100, 当前算符: D_(0, 3) * D_(95, 96), 耗时: 10.603s
[2025-07-24 03:10:37] y方向算符进度: 99/100, 当前算符: D_(0, 3) * D_(96, 99), 耗时: 10.646s
[2025-07-24 03:10:47] y方向算符进度: 100/100, 当前算符: D_(0, 3) * D_(97, 98), 耗时: 10.576s
[2025-07-24 03:10:47] y方向二聚体相关函数计算完成,耗时: 1053.91 秒
[2025-07-24 03:10:48] 计算傅里叶变换...
[2025-07-24 03:10:48] 二聚体结构因子计算完成
[2025-07-24 03:10:49] 二聚体相关函数平均误差: 0.000576
[2025-07-24 03:10:49] 恢复原始样本数: 4096
[2025-07-24 03:10:49] ================================================================================
[2025-07-24 03:10:49] 所有分析完成
