[2025-07-24 00:42:16] ================================================================================
[2025-07-24 00:42:16] 加载量子态: L=5, J2=0.00, J1=0.04
[2025-07-24 00:42:16] 设置样本数为: 1048576
[2025-07-24 00:42:16] 开始生成共享样本集...
[2025-07-24 00:45:35] 样本生成完成,耗时: 199.279 秒
[2025-07-24 00:45:35] ================================================================================
[2025-07-24 00:45:35] 开始计算自旋结构因子...
[2025-07-24 00:45:35] 初始化操作符缓存...
[2025-07-24 00:45:35] 预构建所有自旋相关操作符...
[2025-07-24 00:45:36] 开始计算自旋相关函数...
[2025-07-24 00:45:46] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 10.452s
[2025-07-24 00:45:58] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 12.138s
[2025-07-24 00:46:04] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.269s
[2025-07-24 00:46:11] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.304s
[2025-07-24 00:46:17] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.269s
[2025-07-24 00:46:23] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.303s
[2025-07-24 00:46:30] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.303s
[2025-07-24 00:46:36] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.271s
[2025-07-24 00:46:42] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.304s
[2025-07-24 00:46:48] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.271s
[2025-07-24 00:46:55] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.304s
[2025-07-24 00:47:01] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.271s
[2025-07-24 00:47:07] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.304s
[2025-07-24 00:47:14] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.302s
[2025-07-24 00:47:20] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.304s
[2025-07-24 00:47:26] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.270s
[2025-07-24 00:47:33] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.304s
[2025-07-24 00:47:39] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.271s
[2025-07-24 00:47:45] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.269s
[2025-07-24 00:47:51] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.303s
[2025-07-24 00:47:58] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.269s
[2025-07-24 00:48:04] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.280s
[2025-07-24 00:48:10] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.305s
[2025-07-24 00:48:17] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.269s
[2025-07-24 00:48:23] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.303s
[2025-07-24 00:48:29] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.303s
[2025-07-24 00:48:35] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.283s
[2025-07-24 00:48:42] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.306s
[2025-07-24 00:48:48] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.273s
[2025-07-24 00:48:54] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.269s
[2025-07-24 00:49:01] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.306s
[2025-07-24 00:49:07] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.306s
[2025-07-24 00:49:13] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.275s
[2025-07-24 00:49:19] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.274s
[2025-07-24 00:49:26] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.303s
[2025-07-24 00:49:32] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.270s
[2025-07-24 00:49:38] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.304s
[2025-07-24 00:49:45] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.307s
[2025-07-24 00:49:51] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.273s
[2025-07-24 00:49:57] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.306s
[2025-07-24 00:50:03] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.274s
[2025-07-24 00:50:10] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.269s
[2025-07-24 00:50:16] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.305s
[2025-07-24 00:50:22] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.271s
[2025-07-24 00:50:29] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.307s
[2025-07-24 00:50:35] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.307s
[2025-07-24 00:50:41] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.270s
[2025-07-24 00:50:47] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.301s
[2025-07-24 00:50:54] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.270s
[2025-07-24 00:51:00] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.268s
[2025-07-24 00:51:06] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.303s
[2025-07-24 00:51:13] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.270s
[2025-07-24 00:51:19] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.270s
[2025-07-24 00:51:25] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.312s
[2025-07-24 00:51:32] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.304s
[2025-07-24 00:51:38] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.275s
[2025-07-24 00:51:44] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.308s
[2025-07-24 00:51:50] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.313s
[2025-07-24 00:51:57] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.277s
[2025-07-24 00:52:03] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.312s
[2025-07-24 00:52:09] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.280s
[2025-07-24 00:52:16] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.276s
[2025-07-24 00:52:22] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.313s
[2025-07-24 00:52:28] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.277s
[2025-07-24 00:52:34] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.308s
[2025-07-24 00:52:41] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.306s
[2025-07-24 00:52:47] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.275s
[2025-07-24 00:52:53] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.311s
[2025-07-24 00:53:00] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.284s
[2025-07-24 00:53:06] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.278s
[2025-07-24 00:53:12] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.312s
[2025-07-24 00:53:19] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.277s
[2025-07-24 00:53:25] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.312s
[2025-07-24 00:53:31] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.309s
[2025-07-24 00:53:37] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.275s
[2025-07-24 00:53:44] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.311s
[2025-07-24 00:53:50] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.287s
[2025-07-24 00:53:56] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.275s
[2025-07-24 00:54:03] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.307s
[2025-07-24 00:54:09] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.272s
[2025-07-24 00:54:15] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.324s
[2025-07-24 00:54:22] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.312s
[2025-07-24 00:54:28] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.280s
[2025-07-24 00:54:34] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.314s
[2025-07-24 00:54:40] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.313s
[2025-07-24 00:54:47] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.313s
[2025-07-24 00:54:53] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.279s
[2025-07-24 00:54:59] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.313s
[2025-07-24 00:55:06] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.278s
[2025-07-24 00:55:12] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.279s
[2025-07-24 00:55:18] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.313s
[2025-07-24 00:55:24] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.286s
[2025-07-24 00:55:31] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.308s
[2025-07-24 00:55:37] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.307s
[2025-07-24 00:55:43] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.274s
[2025-07-24 00:55:50] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.304s
[2025-07-24 00:55:56] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.275s
[2025-07-24 00:56:02] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.278s
[2025-07-24 00:56:09] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.311s
[2025-07-24 00:56:15] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.277s
[2025-07-24 00:56:15] 自旋相关函数计算完成,总耗时 639.27 秒
[2025-07-24 00:56:15] 计算傅里叶变换...
[2025-07-24 00:56:16] 自旋结构因子计算完成
[2025-07-24 00:56:17] 自旋相关函数平均误差: 0.000728
[2025-07-24 00:56:17] ================================================================================
[2025-07-24 00:56:17] 开始计算二聚体结构因子...
[2025-07-24 00:56:17] 识别x和y方向的二聚体...
[2025-07-24 00:56:17] 找到 100 个x方向二聚体和 100 个y方向二聚体
[2025-07-24 00:56:17] 预计算二聚体操作符...
[2025-07-24 00:56:18] 使用x-二聚体 (0, 1) (全局索引 0) 作为参考点, 计算x方向连通二聚体相关函数...
[2025-07-24 00:56:24] x方向算符进度: 1/100, 当前算符: D_(0, 1) * D_(0, 1), 耗时: 6.346s
[2025-07-24 00:56:43] x方向算符进度: 2/100, 当前算符: D_(0, 1) * D_(0, 81), 耗时: 18.997s
[2025-07-24 00:56:52] x方向算符进度: 3/100, 当前算符: D_(0, 1) * D_(1, 20), 耗时: 8.785s
[2025-07-24 00:57:08] x方向算符进度: 4/100, 当前算符: D_(0, 1) * D_(2, 3), 耗时: 16.346s
[2025-07-24 00:57:19] x方向算符进度: 5/100, 当前算符: D_(0, 1) * D_(2, 23), 耗时: 10.530s
[2025-07-24 00:57:29] x方向算符进度: 6/100, 当前算符: D_(0, 1) * D_(3, 82), 耗时: 10.593s
[2025-07-24 00:57:40] x方向算符进度: 7/100, 当前算符: D_(0, 1) * D_(4, 5), 耗时: 10.531s
[2025-07-24 00:57:50] x方向算符进度: 8/100, 当前算符: D_(0, 1) * D_(4, 85), 耗时: 10.596s
[2025-07-24 00:58:01] x方向算符进度: 9/100, 当前算符: D_(0, 1) * D_(5, 24), 耗时: 10.597s
[2025-07-24 00:58:12] x方向算符进度: 10/100, 当前算符: D_(0, 1) * D_(6, 7), 耗时: 10.530s
[2025-07-24 00:58:22] x方向算符进度: 11/100, 当前算符: D_(0, 1) * D_(6, 27), 耗时: 10.592s
[2025-07-24 00:58:33] x方向算符进度: 12/100, 当前算符: D_(0, 1) * D_(7, 86), 耗时: 10.597s
[2025-07-24 00:58:43] x方向算符进度: 13/100, 当前算符: D_(0, 1) * D_(8, 9), 耗时: 10.534s
[2025-07-24 00:58:54] x方向算符进度: 14/100, 当前算符: D_(0, 1) * D_(8, 89), 耗时: 10.593s
[2025-07-24 00:59:04] x方向算符进度: 15/100, 当前算符: D_(0, 1) * D_(9, 28), 耗时: 10.530s
[2025-07-24 00:59:15] x方向算符进度: 16/100, 当前算符: D_(0, 1) * D_(10, 11), 耗时: 10.592s
[2025-07-24 00:59:26] x方向算符进度: 17/100, 当前算符: D_(0, 1) * D_(10, 31), 耗时: 10.550s
[2025-07-24 00:59:36] x方向算符进度: 18/100, 当前算符: D_(0, 1) * D_(11, 90), 耗时: 10.545s
[2025-07-24 00:59:47] x方向算符进度: 19/100, 当前算符: D_(0, 1) * D_(12, 13), 耗时: 10.593s
[2025-07-24 00:59:57] x方向算符进度: 20/100, 当前算符: D_(0, 1) * D_(12, 93), 耗时: 10.530s
[2025-07-24 01:00:08] x方向算符进度: 21/100, 当前算符: D_(0, 1) * D_(13, 32), 耗时: 10.593s
[2025-07-24 01:00:18] x方向算符进度: 22/100, 当前算符: D_(0, 1) * D_(14, 15), 耗时: 10.529s
[2025-07-24 01:00:29] x方向算符进度: 23/100, 当前算符: D_(0, 1) * D_(14, 35), 耗时: 10.594s
[2025-07-24 01:00:39] x方向算符进度: 24/100, 当前算符: D_(0, 1) * D_(15, 94), 耗时: 10.530s
[2025-07-24 01:00:50] x方向算符进度: 25/100, 当前算符: D_(0, 1) * D_(16, 17), 耗时: 10.597s
[2025-07-24 01:01:01] x方向算符进度: 26/100, 当前算符: D_(0, 1) * D_(16, 97), 耗时: 10.595s
[2025-07-24 01:01:11] x方向算符进度: 27/100, 当前算符: D_(0, 1) * D_(17, 36), 耗时: 10.532s
[2025-07-24 01:01:22] x方向算符进度: 28/100, 当前算符: D_(0, 1) * D_(18, 19), 耗时: 10.592s
[2025-07-24 01:01:32] x方向算符进度: 29/100, 当前算符: D_(0, 1) * D_(18, 39), 耗时: 10.529s
[2025-07-24 01:01:43] x方向算符进度: 30/100, 当前算符: D_(0, 1) * D_(19, 98), 耗时: 10.597s
[2025-07-24 01:01:54] x方向算符进度: 31/100, 当前算符: D_(0, 1) * D_(20, 21), 耗时: 10.595s
[2025-07-24 01:02:04] x方向算符进度: 32/100, 当前算符: D_(0, 1) * D_(21, 40), 耗时: 10.534s
[2025-07-24 01:02:15] x方向算符进度: 33/100, 当前算符: D_(0, 1) * D_(22, 23), 耗时: 10.594s
[2025-07-24 01:02:25] x方向算符进度: 34/100, 当前算符: D_(0, 1) * D_(22, 43), 耗时: 10.534s
[2025-07-24 01:02:36] x方向算符进度: 35/100, 当前算符: D_(0, 1) * D_(24, 25), 耗时: 10.593s
[2025-07-24 01:02:46] x方向算符进度: 36/100, 当前算符: D_(0, 1) * D_(25, 44), 耗时: 10.530s
[2025-07-24 01:02:57] x方向算符进度: 37/100, 当前算符: D_(0, 1) * D_(26, 27), 耗时: 10.591s
[2025-07-24 01:03:07] x方向算符进度: 38/100, 当前算符: D_(0, 1) * D_(26, 47), 耗时: 10.528s
[2025-07-24 01:03:18] x方向算符进度: 39/100, 当前算符: D_(0, 1) * D_(28, 29), 耗时: 10.594s
[2025-07-24 01:03:29] x方向算符进度: 40/100, 当前算符: D_(0, 1) * D_(29, 48), 耗时: 10.531s
[2025-07-24 01:03:39] x方向算符进度: 41/100, 当前算符: D_(0, 1) * D_(30, 31), 耗时: 10.591s
[2025-07-24 01:03:50] x方向算符进度: 42/100, 当前算符: D_(0, 1) * D_(30, 51), 耗时: 10.531s
[2025-07-24 01:04:00] x方向算符进度: 43/100, 当前算符: D_(0, 1) * D_(32, 33), 耗时: 10.530s
[2025-07-24 01:04:11] x方向算符进度: 44/100, 当前算符: D_(0, 1) * D_(33, 52), 耗时: 10.594s
[2025-07-24 01:04:21] x方向算符进度: 45/100, 当前算符: D_(0, 1) * D_(34, 35), 耗时: 10.528s
[2025-07-24 01:04:32] x方向算符进度: 46/100, 当前算符: D_(0, 1) * D_(34, 55), 耗时: 10.591s
[2025-07-24 01:04:43] x方向算符进度: 47/100, 当前算符: D_(0, 1) * D_(36, 37), 耗时: 10.591s
[2025-07-24 01:04:53] x方向算符进度: 48/100, 当前算符: D_(0, 1) * D_(37, 56), 耗时: 10.529s
[2025-07-24 01:05:04] x方向算符进度: 49/100, 当前算符: D_(0, 1) * D_(38, 39), 耗时: 10.592s
[2025-07-24 01:05:14] x方向算符进度: 50/100, 当前算符: D_(0, 1) * D_(38, 59), 耗时: 10.529s
[2025-07-24 01:05:25] x方向算符进度: 51/100, 当前算符: D_(0, 1) * D_(40, 41), 耗时: 10.592s
[2025-07-24 01:05:35] x方向算符进度: 52/100, 当前算符: D_(0, 1) * D_(41, 60), 耗时: 10.530s
[2025-07-24 01:05:46] x方向算符进度: 53/100, 当前算符: D_(0, 1) * D_(42, 43), 耗时: 10.594s
[2025-07-24 01:05:56] x方向算符进度: 54/100, 当前算符: D_(0, 1) * D_(42, 63), 耗时: 10.529s
[2025-07-24 01:06:07] x方向算符进度: 55/100, 当前算符: D_(0, 1) * D_(44, 45), 耗时: 10.597s
[2025-07-24 01:06:18] x方向算符进度: 56/100, 当前算符: D_(0, 1) * D_(45, 64), 耗时: 10.530s
[2025-07-24 01:06:28] x方向算符进度: 57/100, 当前算符: D_(0, 1) * D_(46, 47), 耗时: 10.592s
[2025-07-24 01:06:39] x方向算符进度: 58/100, 当前算符: D_(0, 1) * D_(46, 67), 耗时: 10.529s
[2025-07-24 01:06:49] x方向算符进度: 59/100, 当前算符: D_(0, 1) * D_(48, 49), 耗时: 10.589s
[2025-07-24 01:07:00] x方向算符进度: 60/100, 当前算符: D_(0, 1) * D_(49, 68), 耗时: 10.529s
[2025-07-24 01:07:10] x方向算符进度: 61/100, 当前算符: D_(0, 1) * D_(50, 51), 耗时: 10.594s
[2025-07-24 01:07:21] x方向算符进度: 62/100, 当前算符: D_(0, 1) * D_(50, 71), 耗时: 10.540s
[2025-07-24 01:07:31] x方向算符进度: 63/100, 当前算符: D_(0, 1) * D_(52, 53), 耗时: 10.545s
[2025-07-24 01:07:42] x方向算符进度: 64/100, 当前算符: D_(0, 1) * D_(53, 72), 耗时: 10.591s
[2025-07-24 01:07:53] x方向算符进度: 65/100, 当前算符: D_(0, 1) * D_(54, 55), 耗时: 10.531s
[2025-07-24 01:08:03] x方向算符进度: 66/100, 当前算符: D_(0, 1) * D_(54, 75), 耗时: 10.593s
[2025-07-24 01:08:14] x方向算符进度: 67/100, 当前算符: D_(0, 1) * D_(56, 57), 耗时: 10.529s
[2025-07-24 01:08:24] x方向算符进度: 68/100, 当前算符: D_(0, 1) * D_(57, 76), 耗时: 10.594s
[2025-07-24 01:08:35] x方向算符进度: 69/100, 当前算符: D_(0, 1) * D_(58, 59), 耗时: 10.765s
[2025-07-24 01:08:46] x方向算符进度: 70/100, 当前算符: D_(0, 1) * D_(58, 79), 耗时: 10.528s
[2025-07-24 01:08:56] x方向算符进度: 71/100, 当前算符: D_(0, 1) * D_(60, 61), 耗时: 10.592s
[2025-07-24 01:09:07] x方向算符进度: 72/100, 当前算符: D_(0, 1) * D_(61, 80), 耗时: 10.530s
[2025-07-24 01:09:17] x方向算符进度: 73/100, 当前算符: D_(0, 1) * D_(62, 63), 耗时: 10.591s
[2025-07-24 01:09:28] x方向算符进度: 74/100, 当前算符: D_(0, 1) * D_(62, 83), 耗时: 10.541s
[2025-07-24 01:09:38] x方向算符进度: 75/100, 当前算符: D_(0, 1) * D_(64, 65), 耗时: 10.591s
[2025-07-24 01:09:49] x方向算符进度: 76/100, 当前算符: D_(0, 1) * D_(65, 84), 耗时: 10.528s
[2025-07-24 01:10:00] x方向算符进度: 77/100, 当前算符: D_(0, 1) * D_(66, 67), 耗时: 10.592s
[2025-07-24 01:10:10] x方向算符进度: 78/100, 当前算符: D_(0, 1) * D_(66, 87), 耗时: 10.532s
[2025-07-24 01:10:21] x方向算符进度: 79/100, 当前算符: D_(0, 1) * D_(68, 69), 耗时: 10.530s
[2025-07-24 01:10:31] x方向算符进度: 80/100, 当前算符: D_(0, 1) * D_(69, 88), 耗时: 10.593s
[2025-07-24 01:10:42] x方向算符进度: 81/100, 当前算符: D_(0, 1) * D_(70, 71), 耗时: 10.532s
[2025-07-24 01:10:52] x方向算符进度: 82/100, 当前算符: D_(0, 1) * D_(70, 91), 耗时: 10.594s
[2025-07-24 01:11:03] x方向算符进度: 83/100, 当前算符: D_(0, 1) * D_(72, 73), 耗时: 10.530s
[2025-07-24 01:11:14] x方向算符进度: 84/100, 当前算符: D_(0, 1) * D_(73, 92), 耗时: 10.595s
[2025-07-24 01:11:24] x方向算符进度: 85/100, 当前算符: D_(0, 1) * D_(74, 75), 耗时: 10.536s
[2025-07-24 01:11:35] x方向算符进度: 86/100, 当前算符: D_(0, 1) * D_(74, 95), 耗时: 10.534s
[2025-07-24 01:11:45] x方向算符进度: 87/100, 当前算符: D_(0, 1) * D_(76, 77), 耗时: 10.596s
[2025-07-24 01:11:56] x方向算符进度: 88/100, 当前算符: D_(0, 1) * D_(77, 96), 耗时: 10.530s
[2025-07-24 01:12:06] x方向算符进度: 89/100, 当前算符: D_(0, 1) * D_(78, 79), 耗时: 10.594s
[2025-07-24 01:12:17] x方向算符进度: 90/100, 当前算符: D_(0, 1) * D_(78, 99), 耗时: 10.532s
[2025-07-24 01:12:27] x方向算符进度: 91/100, 当前算符: D_(0, 1) * D_(80, 81), 耗时: 10.597s
[2025-07-24 01:12:38] x方向算符进度: 92/100, 当前算符: D_(0, 1) * D_(82, 83), 耗时: 10.530s
[2025-07-24 01:12:49] x方向算符进度: 93/100, 当前算符: D_(0, 1) * D_(84, 85), 耗时: 10.592s
[2025-07-24 01:12:59] x方向算符进度: 94/100, 当前算符: D_(0, 1) * D_(86, 87), 耗时: 10.530s
[2025-07-24 01:13:10] x方向算符进度: 95/100, 当前算符: D_(0, 1) * D_(88, 89), 耗时: 10.529s
[2025-07-24 01:13:20] x方向算符进度: 96/100, 当前算符: D_(0, 1) * D_(90, 91), 耗时: 10.595s
[2025-07-24 01:13:31] x方向算符进度: 97/100, 当前算符: D_(0, 1) * D_(92, 93), 耗时: 10.529s
[2025-07-24 01:13:41] x方向算符进度: 98/100, 当前算符: D_(0, 1) * D_(94, 95), 耗时: 10.592s
[2025-07-24 01:13:52] x方向算符进度: 99/100, 当前算符: D_(0, 1) * D_(96, 97), 耗时: 10.531s
[2025-07-24 01:14:02] x方向算符进度: 100/100, 当前算符: D_(0, 1) * D_(98, 99), 耗时: 10.595s
[2025-07-24 01:14:02] x方向二聚体相关函数计算完成,耗时: 1064.76 秒
[2025-07-24 01:14:02] --------------------------------------------------------------------------------
[2025-07-24 01:14:02] 使用y-二聚体 (0, 3) (全局索引 100) 作为参考点, 计算y方向连通二聚体相关函数...
[2025-07-24 01:14:09] y方向算符进度: 1/100, 当前算符: D_(0, 3) * D_(0, 3), 耗时: 6.285s
[2025-07-24 01:14:18] y方向算符进度: 2/100, 当前算符: D_(0, 3) * D_(0, 19), 耗时: 8.815s
[2025-07-24 01:14:28] y方向算符进度: 3/100, 当前算符: D_(0, 3) * D_(1, 2), 耗时: 10.628s
[2025-07-24 01:14:39] y方向算符进度: 4/100, 当前算符: D_(0, 3) * D_(1, 18), 耗时: 10.563s
[2025-07-24 01:14:49] y方向算符进度: 5/100, 当前算符: D_(0, 3) * D_(2, 5), 耗时: 10.563s
[2025-07-24 01:14:58] y方向算符进度: 6/100, 当前算符: D_(0, 3) * D_(3, 4), 耗时: 8.810s
[2025-07-24 01:15:09] y方向算符进度: 7/100, 当前算符: D_(0, 3) * D_(4, 7), 耗时: 10.560s
[2025-07-24 01:15:19] y方向算符进度: 8/100, 当前算符: D_(0, 3) * D_(5, 6), 耗时: 10.632s
[2025-07-24 01:15:30] y方向算符进度: 9/100, 当前算符: D_(0, 3) * D_(6, 9), 耗时: 10.565s
[2025-07-24 01:15:41] y方向算符进度: 10/100, 当前算符: D_(0, 3) * D_(7, 8), 耗时: 10.633s
[2025-07-24 01:15:51] y方向算符进度: 11/100, 当前算符: D_(0, 3) * D_(8, 11), 耗时: 10.558s
[2025-07-24 01:16:02] y方向算符进度: 12/100, 当前算符: D_(0, 3) * D_(9, 10), 耗时: 10.629s
[2025-07-24 01:16:12] y方向算符进度: 13/100, 当前算符: D_(0, 3) * D_(10, 13), 耗时: 10.559s
[2025-07-24 01:16:23] y方向算符进度: 14/100, 当前算符: D_(0, 3) * D_(11, 12), 耗时: 10.620s
[2025-07-24 01:16:33] y方向算符进度: 15/100, 当前算符: D_(0, 3) * D_(12, 15), 耗时: 10.583s
[2025-07-24 01:16:44] y方向算符进度: 16/100, 当前算符: D_(0, 3) * D_(13, 14), 耗时: 10.623s
[2025-07-24 01:16:55] y方向算符进度: 17/100, 当前算符: D_(0, 3) * D_(14, 17), 耗时: 10.570s
[2025-07-24 01:17:05] y方向算符进度: 18/100, 当前算符: D_(0, 3) * D_(15, 16), 耗时: 10.556s
[2025-07-24 01:17:16] y方向算符进度: 19/100, 当前算符: D_(0, 3) * D_(16, 19), 耗时: 10.639s
[2025-07-24 01:17:26] y方向算符进度: 20/100, 当前算符: D_(0, 3) * D_(17, 18), 耗时: 10.561s
[2025-07-24 01:17:37] y方向算符进度: 21/100, 当前算符: D_(0, 3) * D_(20, 23), 耗时: 10.630s
[2025-07-24 01:17:48] y方向算符进度: 22/100, 当前算符: D_(0, 3) * D_(20, 39), 耗时: 10.560s
[2025-07-24 01:17:58] y方向算符进度: 23/100, 当前算符: D_(0, 3) * D_(21, 22), 耗时: 10.635s
[2025-07-24 01:18:09] y方向算符进度: 24/100, 当前算符: D_(0, 3) * D_(21, 38), 耗时: 10.566s
[2025-07-24 01:18:19] y方向算符进度: 25/100, 当前算符: D_(0, 3) * D_(22, 25), 耗时: 10.626s
[2025-07-24 01:18:30] y方向算符进度: 26/100, 当前算符: D_(0, 3) * D_(23, 24), 耗时: 10.556s
[2025-07-24 01:18:41] y方向算符进度: 27/100, 当前算符: D_(0, 3) * D_(24, 27), 耗时: 10.636s
[2025-07-24 01:18:51] y方向算符进度: 28/100, 当前算符: D_(0, 3) * D_(25, 26), 耗时: 10.560s
[2025-07-24 01:19:02] y方向算符进度: 29/100, 当前算符: D_(0, 3) * D_(26, 29), 耗时: 10.641s
[2025-07-24 01:19:12] y方向算符进度: 30/100, 当前算符: D_(0, 3) * D_(27, 28), 耗时: 10.622s
[2025-07-24 01:19:23] y方向算符进度: 31/100, 当前算符: D_(0, 3) * D_(28, 31), 耗时: 10.569s
[2025-07-24 01:19:34] y方向算符进度: 32/100, 当前算符: D_(0, 3) * D_(29, 30), 耗时: 10.631s
[2025-07-24 01:19:44] y方向算符进度: 33/100, 当前算符: D_(0, 3) * D_(30, 33), 耗时: 10.567s
[2025-07-24 01:19:55] y方向算符进度: 34/100, 当前算符: D_(0, 3) * D_(31, 32), 耗时: 10.567s
[2025-07-24 01:20:05] y方向算符进度: 35/100, 当前算符: D_(0, 3) * D_(32, 35), 耗时: 10.622s
[2025-07-24 01:20:16] y方向算符进度: 36/100, 当前算符: D_(0, 3) * D_(33, 34), 耗时: 10.557s
[2025-07-24 01:20:27] y方向算符进度: 37/100, 当前算符: D_(0, 3) * D_(34, 37), 耗时: 10.631s
[2025-07-24 01:20:37] y方向算符进度: 38/100, 当前算符: D_(0, 3) * D_(35, 36), 耗时: 10.556s
[2025-07-24 01:20:48] y方向算符进度: 39/100, 当前算符: D_(0, 3) * D_(36, 39), 耗时: 10.642s
[2025-07-24 01:20:58] y方向算符进度: 40/100, 当前算符: D_(0, 3) * D_(37, 38), 耗时: 10.561s
[2025-07-24 01:21:09] y方向算符进度: 41/100, 当前算符: D_(0, 3) * D_(40, 43), 耗时: 10.637s
[2025-07-24 01:21:20] y方向算符进度: 42/100, 当前算符: D_(0, 3) * D_(40, 59), 耗时: 10.566s
[2025-07-24 01:21:30] y方向算符进度: 43/100, 当前算符: D_(0, 3) * D_(41, 42), 耗时: 10.634s
[2025-07-24 01:21:41] y方向算符进度: 44/100, 当前算符: D_(0, 3) * D_(41, 58), 耗时: 10.568s
[2025-07-24 01:21:51] y方向算符进度: 45/100, 当前算符: D_(0, 3) * D_(42, 45), 耗时: 10.624s
[2025-07-24 01:22:02] y方向算符进度: 46/100, 当前算符: D_(0, 3) * D_(43, 44), 耗时: 10.632s
[2025-07-24 01:22:13] y方向算符进度: 47/100, 当前算符: D_(0, 3) * D_(44, 47), 耗时: 10.564s
[2025-07-24 01:22:23] y方向算符进度: 48/100, 当前算符: D_(0, 3) * D_(45, 46), 耗时: 10.637s
[2025-07-24 01:22:34] y方向算符进度: 49/100, 当前算符: D_(0, 3) * D_(46, 49), 耗时: 10.559s
[2025-07-24 01:22:44] y方向算符进度: 50/100, 当前算符: D_(0, 3) * D_(47, 48), 耗时: 10.627s
[2025-07-24 01:22:55] y方向算符进度: 51/100, 当前算符: D_(0, 3) * D_(48, 51), 耗时: 10.631s
[2025-07-24 01:23:06] y方向算符进度: 52/100, 当前算符: D_(0, 3) * D_(49, 50), 耗时: 10.566s
[2025-07-24 01:23:16] y方向算符进度: 53/100, 当前算符: D_(0, 3) * D_(50, 53), 耗时: 10.625s
[2025-07-24 01:23:27] y方向算符进度: 54/100, 当前算符: D_(0, 3) * D_(51, 52), 耗时: 10.566s
[2025-07-24 01:23:37] y方向算符进度: 55/100, 当前算符: D_(0, 3) * D_(52, 55), 耗时: 10.638s
[2025-07-24 01:23:48] y方向算符进度: 56/100, 当前算符: D_(0, 3) * D_(53, 54), 耗时: 10.589s
[2025-07-24 01:23:59] y方向算符进度: 57/100, 当前算符: D_(0, 3) * D_(54, 57), 耗时: 10.630s
[2025-07-24 01:24:09] y方向算符进度: 58/100, 当前算符: D_(0, 3) * D_(55, 56), 耗时: 10.559s
[2025-07-24 01:24:20] y方向算符进度: 59/100, 当前算符: D_(0, 3) * D_(56, 59), 耗时: 10.641s
[2025-07-24 01:24:30] y方向算符进度: 60/100, 当前算符: D_(0, 3) * D_(57, 58), 耗时: 10.566s
[2025-07-24 01:24:41] y方向算符进度: 61/100, 当前算符: D_(0, 3) * D_(60, 63), 耗时: 10.631s
[2025-07-24 01:24:52] y方向算符进度: 62/100, 当前算符: D_(0, 3) * D_(60, 79), 耗时: 10.638s
[2025-07-24 01:25:02] y方向算符进度: 63/100, 当前算符: D_(0, 3) * D_(61, 62), 耗时: 10.559s
[2025-07-24 01:25:13] y方向算符进度: 64/100, 当前算符: D_(0, 3) * D_(61, 78), 耗时: 10.645s
[2025-07-24 01:25:24] y方向算符进度: 65/100, 当前算符: D_(0, 3) * D_(62, 65), 耗时: 10.570s
[2025-07-24 01:25:34] y方向算符进度: 66/100, 当前算符: D_(0, 3) * D_(63, 64), 耗时: 10.619s
[2025-07-24 01:25:45] y方向算符进度: 67/100, 当前算符: D_(0, 3) * D_(64, 67), 耗时: 10.584s
[2025-07-24 01:25:55] y方向算符进度: 68/100, 当前算符: D_(0, 3) * D_(65, 66), 耗时: 10.562s
[2025-07-24 01:26:06] y方向算符进度: 69/100, 当前算符: D_(0, 3) * D_(66, 69), 耗时: 10.627s
[2025-07-24 01:26:16] y方向算符进度: 70/100, 当前算符: D_(0, 3) * D_(67, 68), 耗时: 10.560s
[2025-07-24 01:26:27] y方向算符进度: 71/100, 当前算符: D_(0, 3) * D_(68, 71), 耗时: 10.628s
[2025-07-24 01:26:38] y方向算符进度: 72/100, 当前算符: D_(0, 3) * D_(69, 70), 耗时: 10.557s
[2025-07-24 01:26:48] y方向算符进度: 73/100, 当前算符: D_(0, 3) * D_(70, 73), 耗时: 10.630s
[2025-07-24 01:26:59] y方向算符进度: 74/100, 当前算符: D_(0, 3) * D_(71, 72), 耗时: 10.567s
[2025-07-24 01:27:09] y方向算符进度: 75/100, 当前算符: D_(0, 3) * D_(72, 75), 耗时: 10.624s
[2025-07-24 01:27:20] y方向算符进度: 76/100, 当前算符: D_(0, 3) * D_(73, 74), 耗时: 10.585s
[2025-07-24 01:27:31] y方向算符进度: 77/100, 当前算符: D_(0, 3) * D_(74, 77), 耗时: 10.621s
[2025-07-24 01:27:41] y方向算符进度: 78/100, 当前算符: D_(0, 3) * D_(75, 76), 耗时: 10.620s
[2025-07-24 01:27:52] y方向算符进度: 79/100, 当前算符: D_(0, 3) * D_(76, 79), 耗时: 10.560s
[2025-07-24 01:28:03] y方向算符进度: 80/100, 当前算符: D_(0, 3) * D_(77, 78), 耗时: 10.627s
[2025-07-24 01:28:13] y方向算符进度: 81/100, 当前算符: D_(0, 3) * D_(80, 83), 耗时: 10.564s
[2025-07-24 01:28:24] y方向算符进度: 82/100, 当前算符: D_(0, 3) * D_(80, 99), 耗时: 10.634s
[2025-07-24 01:28:34] y方向算符进度: 83/100, 当前算符: D_(0, 3) * D_(81, 82), 耗时: 10.559s
[2025-07-24 01:28:45] y方向算符进度: 84/100, 当前算符: D_(0, 3) * D_(81, 98), 耗时: 10.631s
[2025-07-24 01:28:55] y方向算符进度: 85/100, 当前算符: D_(0, 3) * D_(82, 85), 耗时: 10.562s
[2025-07-24 01:29:06] y方向算符进度: 86/100, 当前算符: D_(0, 3) * D_(83, 84), 耗时: 10.620s
[2025-07-24 01:29:17] y方向算符进度: 87/100, 当前算符: D_(0, 3) * D_(84, 87), 耗时: 10.563s
[2025-07-24 01:29:27] y方向算符进度: 88/100, 当前算符: D_(0, 3) * D_(85, 86), 耗时: 10.629s
[2025-07-24 01:29:38] y方向算符进度: 89/100, 当前算符: D_(0, 3) * D_(86, 89), 耗时: 10.566s
[2025-07-24 01:29:48] y方向算符进度: 90/100, 当前算符: D_(0, 3) * D_(87, 88), 耗时: 10.642s
[2025-07-24 01:29:59] y方向算符进度: 91/100, 当前算符: D_(0, 3) * D_(88, 91), 耗时: 10.557s
[2025-07-24 01:30:10] y方向算符进度: 92/100, 当前算符: D_(0, 3) * D_(89, 90), 耗时: 10.636s
[2025-07-24 01:30:20] y方向算符进度: 93/100, 当前算符: D_(0, 3) * D_(90, 93), 耗时: 10.562s
[2025-07-24 01:30:31] y方向算符进度: 94/100, 当前算符: D_(0, 3) * D_(91, 92), 耗时: 10.585s
[2025-07-24 01:30:41] y方向算符进度: 95/100, 当前算符: D_(0, 3) * D_(92, 95), 耗时: 10.628s
[2025-07-24 01:30:52] y方向算符进度: 96/100, 当前算符: D_(0, 3) * D_(93, 94), 耗时: 10.564s
[2025-07-24 01:31:03] y方向算符进度: 97/100, 当前算符: D_(0, 3) * D_(94, 97), 耗时: 10.636s
[2025-07-24 01:31:13] y方向算符进度: 98/100, 当前算符: D_(0, 3) * D_(95, 96), 耗时: 10.563s
[2025-07-24 01:31:24] y方向算符进度: 99/100, 当前算符: D_(0, 3) * D_(96, 99), 耗时: 10.632s
[2025-07-24 01:31:34] y方向算符进度: 100/100, 当前算符: D_(0, 3) * D_(97, 98), 耗时: 10.558s
[2025-07-24 01:31:34] y方向二聚体相关函数计算完成,耗时: 1051.95 秒
[2025-07-24 01:31:35] 计算傅里叶变换...
[2025-07-24 01:31:35] 二聚体结构因子计算完成
[2025-07-24 01:31:36] 二聚体相关函数平均误差: 0.000554
[2025-07-24 01:31:36] 恢复原始样本数: 4096
[2025-07-24 01:31:36] ================================================================================
[2025-07-24 01:31:36] 所有分析完成
