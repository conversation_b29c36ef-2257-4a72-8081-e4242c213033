[2025-07-24 04:00:45] ================================================================================
[2025-07-24 04:00:45] 加载量子态: L=5, J2=0.00, J1=0.08
[2025-07-24 04:00:45] 设置样本数为: 1048576
[2025-07-24 04:00:45] 开始生成共享样本集...
[2025-07-24 04:04:05] 样本生成完成,耗时: 199.904 秒
[2025-07-24 04:04:05] ================================================================================
[2025-07-24 04:04:05] 开始计算自旋结构因子...
[2025-07-24 04:04:05] 初始化操作符缓存...
[2025-07-24 04:04:05] 预构建所有自旋相关操作符...
[2025-07-24 04:04:05] 开始计算自旋相关函数...
[2025-07-24 04:04:16] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 10.418s
[2025-07-24 04:04:28] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 12.121s
[2025-07-24 04:04:34] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.315s
[2025-07-24 04:04:41] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.311s
[2025-07-24 04:04:47] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.302s
[2025-07-24 04:04:53] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.302s
[2025-07-24 04:05:00] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.311s
[2025-07-24 04:05:06] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.302s
[2025-07-24 04:05:12] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.302s
[2025-07-24 04:05:18] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.301s
[2025-07-24 04:05:25] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.302s
[2025-07-24 04:05:31] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.302s
[2025-07-24 04:05:37] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.313s
[2025-07-24 04:05:44] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.312s
[2025-07-24 04:05:50] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.320s
[2025-07-24 04:05:56] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.301s
[2025-07-24 04:06:03] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.302s
[2025-07-24 04:06:09] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.301s
[2025-07-24 04:06:15] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.317s
[2025-07-24 04:06:22] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.317s
[2025-07-24 04:06:28] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.301s
[2025-07-24 04:06:34] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.301s
[2025-07-24 04:06:40] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.303s
[2025-07-24 04:06:47] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.315s
[2025-07-24 04:06:53] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.318s
[2025-07-24 04:06:59] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.312s
[2025-07-24 04:07:06] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.301s
[2025-07-24 04:07:12] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.301s
[2025-07-24 04:07:18] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.301s
[2025-07-24 04:07:25] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.312s
[2025-07-24 04:07:31] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.313s
[2025-07-24 04:07:37] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.318s
[2025-07-24 04:07:44] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.301s
[2025-07-24 04:07:50] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.301s
[2025-07-24 04:07:56] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.302s
[2025-07-24 04:08:02] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.302s
[2025-07-24 04:08:09] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.302s
[2025-07-24 04:08:15] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.302s
[2025-07-24 04:08:21] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.319s
[2025-07-24 04:08:28] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.317s
[2025-07-24 04:08:34] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.314s
[2025-07-24 04:08:40] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.315s
[2025-07-24 04:08:47] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.312s
[2025-07-24 04:08:53] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.318s
[2025-07-24 04:08:59] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.302s
[2025-07-24 04:09:06] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.302s
[2025-07-24 04:09:12] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.313s
[2025-07-24 04:09:18] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.317s
[2025-07-24 04:09:25] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.315s
[2025-07-24 04:09:31] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.301s
[2025-07-24 04:09:37] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.301s
[2025-07-24 04:09:43] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.301s
[2025-07-24 04:09:50] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.318s
[2025-07-24 04:09:56] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.308s
[2025-07-24 04:10:02] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.301s
[2025-07-24 04:10:09] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.301s
[2025-07-24 04:10:15] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.301s
[2025-07-24 04:10:21] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.313s
[2025-07-24 04:10:28] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.314s
[2025-07-24 04:10:34] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.311s
[2025-07-24 04:10:40] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.301s
[2025-07-24 04:10:47] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.301s
[2025-07-24 04:10:53] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.301s
[2025-07-24 04:10:59] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.318s
[2025-07-24 04:11:05] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.317s
[2025-07-24 04:11:12] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.308s
[2025-07-24 04:11:18] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.301s
[2025-07-24 04:11:24] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.301s
[2025-07-24 04:11:31] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.317s
[2025-07-24 04:11:37] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.312s
[2025-07-24 04:11:43] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.311s
[2025-07-24 04:11:50] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.301s
[2025-07-24 04:11:56] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.300s
[2025-07-24 04:12:02] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.301s
[2025-07-24 04:12:09] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.311s
[2025-07-24 04:12:15] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.317s
[2025-07-24 04:12:21] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.301s
[2025-07-24 04:12:27] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.301s
[2025-07-24 04:12:34] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.300s
[2025-07-24 04:12:40] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.317s
[2025-07-24 04:12:46] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.313s
[2025-07-24 04:12:53] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.302s
[2025-07-24 04:12:59] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.301s
[2025-07-24 04:13:05] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.302s
[2025-07-24 04:13:12] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.312s
[2025-07-24 04:13:18] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.321s
[2025-07-24 04:13:24] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.317s
[2025-07-24 04:13:31] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.301s
[2025-07-24 04:13:37] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.301s
[2025-07-24 04:13:43] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.318s
[2025-07-24 04:13:50] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.320s
[2025-07-24 04:13:56] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.319s
[2025-07-24 04:14:02] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.301s
[2025-07-24 04:14:09] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.399s
[2025-07-24 04:14:15] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.301s
[2025-07-24 04:14:21] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.301s
[2025-07-24 04:14:27] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.318s
[2025-07-24 04:14:34] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.312s
[2025-07-24 04:14:40] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.310s
[2025-07-24 04:14:46] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.302s
[2025-07-24 04:14:46] 自旋相关函数计算完成,总耗时 640.93 秒
[2025-07-24 04:14:47] 计算傅里叶变换...
[2025-07-24 04:14:47] 自旋结构因子计算完成
[2025-07-24 04:14:48] 自旋相关函数平均误差: 0.000702
[2025-07-24 04:14:48] ================================================================================
[2025-07-24 04:14:48] 开始计算二聚体结构因子...
[2025-07-24 04:14:48] 识别x和y方向的二聚体...
[2025-07-24 04:14:48] 找到 100 个x方向二聚体和 100 个y方向二聚体
[2025-07-24 04:14:48] 预计算二聚体操作符...
[2025-07-24 04:14:49] 使用x-二聚体 (0, 1) (全局索引 0) 作为参考点, 计算x方向连通二聚体相关函数...
[2025-07-24 04:14:56] x方向算符进度: 1/100, 当前算符: D_(0, 1) * D_(0, 1), 耗时: 6.335s
[2025-07-24 04:15:15] x方向算符进度: 2/100, 当前算符: D_(0, 1) * D_(0, 81), 耗时: 19.016s
[2025-07-24 04:15:23] x方向算符进度: 3/100, 当前算符: D_(0, 1) * D_(1, 20), 耗时: 8.842s
[2025-07-24 04:15:40] x方向算符进度: 4/100, 当前算符: D_(0, 1) * D_(2, 3), 耗时: 16.312s
[2025-07-24 04:15:50] x方向算符进度: 5/100, 当前算符: D_(0, 1) * D_(2, 23), 耗时: 10.591s
[2025-07-24 04:16:01] x方向算符进度: 6/100, 当前算符: D_(0, 1) * D_(3, 82), 耗时: 10.608s
[2025-07-24 04:16:12] x方向算符进度: 7/100, 当前算符: D_(0, 1) * D_(4, 5), 耗时: 10.590s
[2025-07-24 04:16:22] x方向算符进度: 8/100, 当前算符: D_(0, 1) * D_(4, 85), 耗时: 10.590s
[2025-07-24 04:16:33] x方向算符进度: 9/100, 当前算符: D_(0, 1) * D_(5, 24), 耗时: 10.590s
[2025-07-24 04:16:43] x方向算符进度: 10/100, 当前算符: D_(0, 1) * D_(6, 7), 耗时: 10.582s
[2025-07-24 04:16:54] x方向算符进度: 11/100, 当前算符: D_(0, 1) * D_(6, 27), 耗时: 10.582s
[2025-07-24 04:17:04] x方向算符进度: 12/100, 当前算符: D_(0, 1) * D_(7, 86), 耗时: 10.587s
[2025-07-24 04:17:15] x方向算符进度: 13/100, 当前算符: D_(0, 1) * D_(8, 9), 耗时: 10.619s
[2025-07-24 04:17:26] x方向算符进度: 14/100, 当前算符: D_(0, 1) * D_(8, 89), 耗时: 10.621s
[2025-07-24 04:17:36] x方向算符进度: 15/100, 当前算符: D_(0, 1) * D_(9, 28), 耗时: 10.587s
[2025-07-24 04:17:47] x方向算符进度: 16/100, 当前算符: D_(0, 1) * D_(10, 11), 耗时: 10.589s
[2025-07-24 04:17:57] x方向算符进度: 17/100, 当前算符: D_(0, 1) * D_(10, 31), 耗时: 10.589s
[2025-07-24 04:18:08] x方向算符进度: 18/100, 当前算符: D_(0, 1) * D_(11, 90), 耗时: 10.610s
[2025-07-24 04:18:19] x方向算符进度: 19/100, 当前算符: D_(0, 1) * D_(12, 13), 耗时: 10.609s
[2025-07-24 04:18:29] x方向算符进度: 20/100, 当前算符: D_(0, 1) * D_(12, 93), 耗时: 10.595s
[2025-07-24 04:18:40] x方向算符进度: 21/100, 当前算符: D_(0, 1) * D_(13, 32), 耗时: 10.587s
[2025-07-24 04:18:50] x方向算符进度: 22/100, 当前算符: D_(0, 1) * D_(14, 15), 耗时: 10.584s
[2025-07-24 04:19:01] x方向算符进度: 23/100, 当前算符: D_(0, 1) * D_(14, 35), 耗时: 10.616s
[2025-07-24 04:19:12] x方向算符进度: 24/100, 当前算符: D_(0, 1) * D_(15, 94), 耗时: 10.630s
[2025-07-24 04:19:22] x方向算符进度: 25/100, 当前算符: D_(0, 1) * D_(16, 17), 耗时: 10.627s
[2025-07-24 04:19:33] x方向算符进度: 26/100, 当前算符: D_(0, 1) * D_(16, 97), 耗时: 10.602s
[2025-07-24 04:19:44] x方向算符进度: 27/100, 当前算符: D_(0, 1) * D_(17, 36), 耗时: 10.589s
[2025-07-24 04:19:54] x方向算符进度: 28/100, 当前算符: D_(0, 1) * D_(18, 19), 耗时: 10.589s
[2025-07-24 04:20:05] x方向算符进度: 29/100, 当前算符: D_(0, 1) * D_(18, 39), 耗时: 10.608s
[2025-07-24 04:20:15] x方向算符进度: 30/100, 当前算符: D_(0, 1) * D_(19, 98), 耗时: 10.631s
[2025-07-24 04:20:26] x方向算符进度: 31/100, 当前算符: D_(0, 1) * D_(20, 21), 耗时: 10.608s
[2025-07-24 04:20:37] x方向算符进度: 32/100, 当前算符: D_(0, 1) * D_(21, 40), 耗时: 10.591s
[2025-07-24 04:20:47] x方向算符进度: 33/100, 当前算符: D_(0, 1) * D_(22, 23), 耗时: 10.582s
[2025-07-24 04:20:58] x方向算符进度: 34/100, 当前算符: D_(0, 1) * D_(22, 43), 耗时: 10.589s
[2025-07-24 04:21:08] x方向算符进度: 35/100, 当前算符: D_(0, 1) * D_(24, 25), 耗时: 10.621s
[2025-07-24 04:21:19] x方向算符进度: 36/100, 当前算符: D_(0, 1) * D_(25, 44), 耗时: 10.607s
[2025-07-24 04:21:30] x方向算符进度: 37/100, 当前算符: D_(0, 1) * D_(26, 27), 耗时: 10.588s
[2025-07-24 04:21:40] x方向算符进度: 38/100, 当前算符: D_(0, 1) * D_(26, 47), 耗时: 10.585s
[2025-07-24 04:21:51] x方向算符进度: 39/100, 当前算符: D_(0, 1) * D_(28, 29), 耗时: 10.610s
[2025-07-24 04:22:01] x方向算符进度: 40/100, 当前算符: D_(0, 1) * D_(29, 48), 耗时: 10.615s
[2025-07-24 04:22:12] x方向算符进度: 41/100, 当前算符: D_(0, 1) * D_(30, 31), 耗时: 10.607s
[2025-07-24 04:22:23] x方向算符进度: 42/100, 当前算符: D_(0, 1) * D_(30, 51), 耗时: 10.621s
[2025-07-24 04:22:33] x方向算符进度: 43/100, 当前算符: D_(0, 1) * D_(32, 33), 耗时: 10.611s
[2025-07-24 04:22:44] x方向算符进度: 44/100, 当前算符: D_(0, 1) * D_(33, 52), 耗时: 10.590s
[2025-07-24 04:22:54] x方向算符进度: 45/100, 当前算符: D_(0, 1) * D_(34, 35), 耗时: 10.589s
[2025-07-24 04:23:05] x方向算符进度: 46/100, 当前算符: D_(0, 1) * D_(34, 55), 耗时: 10.590s
[2025-07-24 04:23:16] x方向算符进度: 47/100, 当前算符: D_(0, 1) * D_(36, 37), 耗时: 10.623s
[2025-07-24 04:23:26] x方向算符进度: 48/100, 当前算符: D_(0, 1) * D_(37, 56), 耗时: 10.607s
[2025-07-24 04:23:37] x方向算符进度: 49/100, 当前算符: D_(0, 1) * D_(38, 39), 耗时: 10.589s
[2025-07-24 04:23:47] x方向算符进度: 50/100, 当前算符: D_(0, 1) * D_(38, 59), 耗时: 10.590s
[2025-07-24 04:23:58] x方向算符进度: 51/100, 当前算符: D_(0, 1) * D_(40, 41), 耗时: 10.582s
[2025-07-24 04:24:09] x方向算符进度: 52/100, 当前算符: D_(0, 1) * D_(41, 60), 耗时: 10.614s
[2025-07-24 04:24:19] x方向算符进度: 53/100, 当前算符: D_(0, 1) * D_(42, 43), 耗时: 10.611s
[2025-07-24 04:24:30] x方向算符进度: 54/100, 当前算符: D_(0, 1) * D_(42, 63), 耗时: 10.606s
[2025-07-24 04:24:40] x方向算符进度: 55/100, 当前算符: D_(0, 1) * D_(44, 45), 耗时: 10.590s
[2025-07-24 04:24:51] x方向算符进度: 56/100, 当前算符: D_(0, 1) * D_(45, 64), 耗时: 10.590s
[2025-07-24 04:25:02] x方向算符进度: 57/100, 当前算符: D_(0, 1) * D_(46, 47), 耗时: 10.674s
[2025-07-24 04:25:12] x方向算符进度: 58/100, 当前算符: D_(0, 1) * D_(46, 67), 耗时: 10.616s
[2025-07-24 04:25:23] x方向算符进度: 59/100, 当前算符: D_(0, 1) * D_(48, 49), 耗时: 10.623s
[2025-07-24 04:25:34] x方向算符进度: 60/100, 当前算符: D_(0, 1) * D_(49, 68), 耗时: 10.589s
[2025-07-24 04:25:44] x方向算符进度: 61/100, 当前算符: D_(0, 1) * D_(50, 51), 耗时: 10.584s
[2025-07-24 04:25:55] x方向算符进度: 62/100, 当前算符: D_(0, 1) * D_(50, 71), 耗时: 10.617s
[2025-07-24 04:26:05] x方向算符进度: 63/100, 当前算符: D_(0, 1) * D_(52, 53), 耗时: 10.629s
[2025-07-24 04:26:16] x方向算符进度: 64/100, 当前算符: D_(0, 1) * D_(53, 72), 耗时: 10.608s
[2025-07-24 04:26:27] x方向算符进度: 65/100, 当前算符: D_(0, 1) * D_(54, 55), 耗时: 10.589s
[2025-07-24 04:26:37] x方向算符进度: 66/100, 当前算符: D_(0, 1) * D_(54, 75), 耗时: 10.582s
[2025-07-24 04:26:48] x方向算符进度: 67/100, 当前算符: D_(0, 1) * D_(56, 57), 耗时: 10.589s
[2025-07-24 04:26:58] x方向算符进度: 68/100, 当前算符: D_(0, 1) * D_(57, 76), 耗时: 10.622s
[2025-07-24 04:27:09] x方向算符进度: 69/100, 当前算符: D_(0, 1) * D_(58, 59), 耗时: 10.618s
[2025-07-24 04:27:20] x方向算符进度: 70/100, 当前算符: D_(0, 1) * D_(58, 79), 耗时: 10.590s
[2025-07-24 04:27:30] x方向算符进度: 71/100, 当前算符: D_(0, 1) * D_(60, 61), 耗时: 10.588s
[2025-07-24 04:27:41] x方向算符进度: 72/100, 当前算符: D_(0, 1) * D_(61, 80), 耗时: 10.589s
[2025-07-24 04:27:51] x方向算符进度: 73/100, 当前算符: D_(0, 1) * D_(62, 63), 耗时: 10.608s
[2025-07-24 04:28:02] x方向算符进度: 74/100, 当前算符: D_(0, 1) * D_(62, 83), 耗时: 10.610s
[2025-07-24 04:28:13] x方向算符进度: 75/100, 当前算符: D_(0, 1) * D_(64, 65), 耗时: 10.595s
[2025-07-24 04:28:23] x方向算符进度: 76/100, 当前算符: D_(0, 1) * D_(65, 84), 耗时: 10.588s
[2025-07-24 04:28:34] x方向算符进度: 77/100, 当前算符: D_(0, 1) * D_(66, 67), 耗时: 10.590s
[2025-07-24 04:28:44] x方向算符进度: 78/100, 当前算符: D_(0, 1) * D_(66, 87), 耗时: 10.590s
[2025-07-24 04:28:55] x方向算符进度: 79/100, 当前算符: D_(0, 1) * D_(68, 69), 耗时: 10.590s
[2025-07-24 04:29:06] x方向算符进度: 80/100, 当前算符: D_(0, 1) * D_(69, 88), 耗时: 10.626s
[2025-07-24 04:29:16] x方向算符进度: 81/100, 当前算符: D_(0, 1) * D_(70, 71), 耗时: 10.623s
[2025-07-24 04:29:27] x方向算符进度: 82/100, 当前算符: D_(0, 1) * D_(70, 91), 耗时: 10.602s
[2025-07-24 04:29:37] x方向算符进度: 83/100, 当前算符: D_(0, 1) * D_(72, 73), 耗时: 10.589s
[2025-07-24 04:29:48] x方向算符进度: 84/100, 当前算符: D_(0, 1) * D_(73, 92), 耗时: 10.590s
[2025-07-24 04:29:59] x方向算符进度: 85/100, 当前算符: D_(0, 1) * D_(74, 75), 耗时: 10.621s
[2025-07-24 04:30:09] x方向算符进度: 86/100, 当前算符: D_(0, 1) * D_(74, 95), 耗时: 10.613s
[2025-07-24 04:30:20] x方向算符进度: 87/100, 当前算符: D_(0, 1) * D_(76, 77), 耗时: 10.606s
[2025-07-24 04:30:30] x方向算符进度: 88/100, 当前算符: D_(0, 1) * D_(77, 96), 耗时: 10.589s
[2025-07-24 04:30:41] x方向算符进度: 89/100, 当前算符: D_(0, 1) * D_(78, 79), 耗时: 10.580s
[2025-07-24 04:30:52] x方向算符进度: 90/100, 当前算符: D_(0, 1) * D_(78, 99), 耗时: 10.589s
[2025-07-24 04:31:02] x方向算符进度: 91/100, 当前算符: D_(0, 1) * D_(80, 81), 耗时: 10.662s
[2025-07-24 04:31:13] x方向算符进度: 92/100, 当前算符: D_(0, 1) * D_(82, 83), 耗时: 10.621s
[2025-07-24 04:31:23] x方向算符进度: 93/100, 当前算符: D_(0, 1) * D_(84, 85), 耗时: 10.623s
[2025-07-24 04:31:34] x方向算符进度: 94/100, 当前算符: D_(0, 1) * D_(86, 87), 耗时: 10.600s
[2025-07-24 04:31:45] x方向算符进度: 95/100, 当前算符: D_(0, 1) * D_(88, 89), 耗时: 10.584s
[2025-07-24 04:31:55] x方向算符进度: 96/100, 当前算符: D_(0, 1) * D_(90, 91), 耗时: 10.589s
[2025-07-24 04:32:06] x方向算符进度: 97/100, 当前算符: D_(0, 1) * D_(92, 93), 耗时: 10.590s
[2025-07-24 04:32:16] x方向算符进度: 98/100, 当前算符: D_(0, 1) * D_(94, 95), 耗时: 10.626s
[2025-07-24 04:32:27] x方向算符进度: 99/100, 当前算符: D_(0, 1) * D_(96, 97), 耗时: 10.623s
[2025-07-24 04:32:38] x方向算符进度: 100/100, 当前算符: D_(0, 1) * D_(98, 99), 耗时: 10.607s
[2025-07-24 04:32:38] x方向二聚体相关函数计算完成,耗时: 1068.45 秒
[2025-07-24 04:32:38] --------------------------------------------------------------------------------
[2025-07-24 04:32:38] 使用y-二聚体 (0, 3) (全局索引 100) 作为参考点, 计算y方向连通二聚体相关函数...
[2025-07-24 04:32:44] y方向算符进度: 1/100, 当前算符: D_(0, 3) * D_(0, 3), 耗时: 6.310s
[2025-07-24 04:32:53] y方向算符进度: 2/100, 当前算符: D_(0, 3) * D_(0, 19), 耗时: 8.832s
[2025-07-24 04:33:03] y方向算符进度: 3/100, 当前算符: D_(0, 3) * D_(1, 2), 耗时: 10.587s
[2025-07-24 04:33:14] y方向算符进度: 4/100, 当前算符: D_(0, 3) * D_(1, 18), 耗时: 10.587s
[2025-07-24 04:33:25] y方向算符进度: 5/100, 当前算符: D_(0, 3) * D_(2, 5), 耗时: 10.586s
[2025-07-24 04:33:33] y方向算符进度: 6/100, 当前算符: D_(0, 3) * D_(3, 4), 耗时: 8.828s
[2025-07-24 04:33:44] y方向算符进度: 7/100, 当前算符: D_(0, 3) * D_(4, 7), 耗时: 10.606s
[2025-07-24 04:33:55] y方向算符进度: 8/100, 当前算符: D_(0, 3) * D_(5, 6), 耗时: 10.594s
[2025-07-24 04:34:05] y方向算符进度: 9/100, 当前算符: D_(0, 3) * D_(6, 9), 耗时: 10.580s
[2025-07-24 04:34:16] y方向算符进度: 10/100, 当前算符: D_(0, 3) * D_(7, 8), 耗时: 10.618s
[2025-07-24 04:34:26] y方向算符进度: 11/100, 当前算符: D_(0, 3) * D_(8, 11), 耗时: 10.588s
[2025-07-24 04:34:37] y方向算符进度: 12/100, 当前算符: D_(0, 3) * D_(9, 10), 耗时: 10.575s
[2025-07-24 04:34:48] y方向算符进度: 13/100, 当前算符: D_(0, 3) * D_(10, 13), 耗时: 10.589s
[2025-07-24 04:34:58] y方向算符进度: 14/100, 当前算符: D_(0, 3) * D_(11, 12), 耗时: 10.609s
[2025-07-24 04:35:09] y方向算符进度: 15/100, 当前算符: D_(0, 3) * D_(12, 15), 耗时: 10.621s
[2025-07-24 04:35:19] y方向算符进度: 16/100, 当前算符: D_(0, 3) * D_(13, 14), 耗时: 10.588s
[2025-07-24 04:35:30] y方向算符进度: 17/100, 当前算符: D_(0, 3) * D_(14, 17), 耗时: 10.575s
[2025-07-24 04:35:41] y方向算符进度: 18/100, 当前算符: D_(0, 3) * D_(15, 16), 耗时: 10.588s
[2025-07-24 04:35:51] y方向算符进度: 19/100, 当前算符: D_(0, 3) * D_(16, 19), 耗时: 10.597s
[2025-07-24 04:36:02] y方向算符进度: 20/100, 当前算符: D_(0, 3) * D_(17, 18), 耗时: 10.619s
[2025-07-24 04:36:12] y方向算符进度: 21/100, 当前算符: D_(0, 3) * D_(20, 23), 耗时: 10.608s
[2025-07-24 04:36:23] y方向算符进度: 22/100, 当前算符: D_(0, 3) * D_(20, 39), 耗时: 10.581s
[2025-07-24 04:36:34] y方向算符进度: 23/100, 当前算符: D_(0, 3) * D_(21, 22), 耗时: 10.579s
[2025-07-24 04:36:44] y方向算符进度: 24/100, 当前算符: D_(0, 3) * D_(21, 38), 耗时: 10.596s
[2025-07-24 04:36:55] y方向算符进度: 25/100, 当前算符: D_(0, 3) * D_(22, 25), 耗时: 10.608s
[2025-07-24 04:37:05] y方向算符进度: 26/100, 当前算符: D_(0, 3) * D_(23, 24), 耗时: 10.611s
[2025-07-24 04:37:16] y方向算符进度: 27/100, 当前算符: D_(0, 3) * D_(24, 27), 耗时: 10.620s
[2025-07-24 04:37:27] y方向算符进度: 28/100, 当前算符: D_(0, 3) * D_(25, 26), 耗时: 10.620s
[2025-07-24 04:37:37] y方向算符进度: 29/100, 当前算符: D_(0, 3) * D_(26, 29), 耗时: 10.588s
[2025-07-24 04:37:48] y方向算符进度: 30/100, 当前算符: D_(0, 3) * D_(27, 28), 耗时: 10.588s
[2025-07-24 04:37:58] y方向算符进度: 31/100, 当前算符: D_(0, 3) * D_(28, 31), 耗时: 10.588s
[2025-07-24 04:38:09] y方向算符进度: 32/100, 当前算符: D_(0, 3) * D_(29, 30), 耗时: 10.620s
[2025-07-24 04:38:20] y方向算符进度: 33/100, 当前算符: D_(0, 3) * D_(30, 33), 耗时: 10.622s
[2025-07-24 04:38:30] y方向算符进度: 34/100, 当前算符: D_(0, 3) * D_(31, 32), 耗时: 10.612s
[2025-07-24 04:38:41] y方向算符进度: 35/100, 当前算符: D_(0, 3) * D_(32, 35), 耗时: 10.588s
[2025-07-24 04:38:51] y方向算符进度: 36/100, 当前算符: D_(0, 3) * D_(33, 34), 耗时: 10.587s
[2025-07-24 04:39:02] y方向算符进度: 37/100, 当前算符: D_(0, 3) * D_(34, 37), 耗时: 10.619s
[2025-07-24 04:39:13] y方向算符进度: 38/100, 当前算符: D_(0, 3) * D_(35, 36), 耗时: 10.623s
[2025-07-24 04:39:23] y方向算符进度: 39/100, 当前算符: D_(0, 3) * D_(36, 39), 耗时: 10.618s
[2025-07-24 04:39:34] y方向算符进度: 40/100, 当前算符: D_(0, 3) * D_(37, 38), 耗时: 10.587s
[2025-07-24 04:39:44] y方向算符进度: 41/100, 当前算符: D_(0, 3) * D_(40, 43), 耗时: 10.583s
[2025-07-24 04:39:55] y方向算符进度: 42/100, 当前算符: D_(0, 3) * D_(40, 59), 耗时: 10.587s
[2025-07-24 04:40:06] y方向算符进度: 43/100, 当前算符: D_(0, 3) * D_(41, 42), 耗时: 10.590s
[2025-07-24 04:40:16] y方向算符进度: 44/100, 当前算符: D_(0, 3) * D_(41, 58), 耗时: 10.618s
[2025-07-24 04:40:27] y方向算符进度: 45/100, 当前算符: D_(0, 3) * D_(42, 45), 耗时: 10.577s
[2025-07-24 04:40:37] y方向算符进度: 46/100, 当前算符: D_(0, 3) * D_(43, 44), 耗时: 10.589s
[2025-07-24 04:40:48] y方向算符进度: 47/100, 当前算符: D_(0, 3) * D_(44, 47), 耗时: 10.588s
[2025-07-24 04:40:59] y方向算符进度: 48/100, 当前算符: D_(0, 3) * D_(45, 46), 耗时: 10.588s
[2025-07-24 04:41:09] y方向算符进度: 49/100, 当前算符: D_(0, 3) * D_(46, 49), 耗时: 10.588s
[2025-07-24 04:41:20] y方向算符进度: 50/100, 当前算符: D_(0, 3) * D_(47, 48), 耗时: 10.584s
[2025-07-24 04:41:30] y方向算符进度: 51/100, 当前算符: D_(0, 3) * D_(48, 51), 耗时: 10.619s
[2025-07-24 04:41:41] y方向算符进度: 52/100, 当前算符: D_(0, 3) * D_(49, 50), 耗时: 10.619s
[2025-07-24 04:41:52] y方向算符进度: 53/100, 当前算符: D_(0, 3) * D_(50, 53), 耗时: 10.587s
[2025-07-24 04:42:02] y方向算符进度: 54/100, 当前算符: D_(0, 3) * D_(51, 52), 耗时: 10.605s
[2025-07-24 04:42:13] y方向算符进度: 55/100, 当前算符: D_(0, 3) * D_(52, 55), 耗时: 10.588s
[2025-07-24 04:42:23] y方向算符进度: 56/100, 当前算符: D_(0, 3) * D_(53, 54), 耗时: 10.589s
[2025-07-24 04:42:34] y方向算符进度: 57/100, 当前算符: D_(0, 3) * D_(54, 57), 耗时: 10.618s
[2025-07-24 04:42:45] y方向算符进度: 58/100, 当前算符: D_(0, 3) * D_(55, 56), 耗时: 10.618s
[2025-07-24 04:42:55] y方向算符进度: 59/100, 当前算符: D_(0, 3) * D_(56, 59), 耗时: 10.575s
[2025-07-24 04:43:06] y方向算符进度: 60/100, 当前算符: D_(0, 3) * D_(57, 58), 耗时: 10.587s
[2025-07-24 04:43:16] y方向算符进度: 61/100, 当前算符: D_(0, 3) * D_(60, 63), 耗时: 10.595s
[2025-07-24 04:43:27] y方向算符进度: 62/100, 当前算符: D_(0, 3) * D_(60, 79), 耗时: 10.619s
[2025-07-24 04:43:38] y方向算符进度: 63/100, 当前算符: D_(0, 3) * D_(61, 62), 耗时: 10.621s
[2025-07-24 04:43:48] y方向算符进度: 64/100, 当前算符: D_(0, 3) * D_(61, 78), 耗时: 10.588s
[2025-07-24 04:43:59] y方向算符进度: 65/100, 当前算符: D_(0, 3) * D_(62, 65), 耗时: 10.588s
[2025-07-24 04:44:09] y方向算符进度: 66/100, 当前算符: D_(0, 3) * D_(63, 64), 耗时: 10.587s
[2025-07-24 04:44:20] y方向算符进度: 67/100, 当前算符: D_(0, 3) * D_(64, 67), 耗时: 10.614s
[2025-07-24 04:44:31] y方向算符进度: 68/100, 当前算符: D_(0, 3) * D_(65, 66), 耗时: 10.609s
[2025-07-24 04:44:41] y方向算符进度: 69/100, 当前算符: D_(0, 3) * D_(66, 69), 耗时: 10.588s
[2025-07-24 04:44:52] y方向算符进度: 70/100, 当前算符: D_(0, 3) * D_(67, 68), 耗时: 10.581s
[2025-07-24 04:45:02] y方向算符进度: 71/100, 当前算符: D_(0, 3) * D_(68, 71), 耗时: 10.588s
[2025-07-24 04:45:13] y方向算符进度: 72/100, 当前算符: D_(0, 3) * D_(69, 70), 耗时: 10.596s
[2025-07-24 04:45:24] y方向算符进度: 73/100, 当前算符: D_(0, 3) * D_(70, 73), 耗时: 10.606s
[2025-07-24 04:45:34] y方向算符进度: 74/100, 当前算符: D_(0, 3) * D_(71, 72), 耗时: 10.619s
[2025-07-24 04:45:45] y方向算符进度: 75/100, 当前算符: D_(0, 3) * D_(72, 75), 耗时: 10.579s
[2025-07-24 04:45:55] y方向算符进度: 76/100, 当前算符: D_(0, 3) * D_(73, 74), 耗时: 10.587s
[2025-07-24 04:46:06] y方向算符进度: 77/100, 当前算符: D_(0, 3) * D_(74, 77), 耗时: 10.610s
[2025-07-24 04:46:17] y方向算符进度: 78/100, 当前算符: D_(0, 3) * D_(75, 76), 耗时: 10.606s
[2025-07-24 04:46:27] y方向算符进度: 79/100, 当前算符: D_(0, 3) * D_(76, 79), 耗时: 10.619s
[2025-07-24 04:46:38] y方向算符进度: 80/100, 当前算符: D_(0, 3) * D_(77, 78), 耗时: 10.587s
[2025-07-24 04:46:48] y方向算符进度: 81/100, 当前算符: D_(0, 3) * D_(80, 83), 耗时: 10.588s
[2025-07-24 04:46:59] y方向算符进度: 82/100, 当前算符: D_(0, 3) * D_(80, 99), 耗时: 10.587s
[2025-07-24 04:47:10] y方向算符进度: 83/100, 当前算符: D_(0, 3) * D_(81, 82), 耗时: 10.609s
[2025-07-24 04:47:20] y方向算符进度: 84/100, 当前算符: D_(0, 3) * D_(81, 98), 耗时: 10.616s
[2025-07-24 04:47:31] y方向算符进度: 85/100, 当前算符: D_(0, 3) * D_(82, 85), 耗时: 10.586s
[2025-07-24 04:47:41] y方向算符进度: 86/100, 当前算符: D_(0, 3) * D_(83, 84), 耗时: 10.585s
[2025-07-24 04:47:52] y方向算符进度: 87/100, 当前算符: D_(0, 3) * D_(84, 87), 耗时: 10.585s
[2025-07-24 04:48:03] y方向算符进度: 88/100, 当前算符: D_(0, 3) * D_(85, 86), 耗时: 10.602s
[2025-07-24 04:48:13] y方向算符进度: 89/100, 当前算符: D_(0, 3) * D_(86, 89), 耗时: 10.603s
[2025-07-24 04:48:24] y方向算符进度: 90/100, 当前算符: D_(0, 3) * D_(87, 88), 耗时: 10.618s
[2025-07-24 04:48:34] y方向算符进度: 91/100, 当前算符: D_(0, 3) * D_(88, 91), 耗时: 10.619s
[2025-07-24 04:48:45] y方向算符进度: 92/100, 当前算符: D_(0, 3) * D_(89, 90), 耗时: 10.608s
[2025-07-24 04:48:56] y方向算符进度: 93/100, 当前算符: D_(0, 3) * D_(90, 93), 耗时: 10.586s
[2025-07-24 04:49:06] y方向算符进度: 94/100, 当前算符: D_(0, 3) * D_(91, 92), 耗时: 10.588s
[2025-07-24 04:49:17] y方向算符进度: 95/100, 当前算符: D_(0, 3) * D_(92, 95), 耗时: 10.580s
[2025-07-24 04:49:27] y方向算符进度: 96/100, 当前算符: D_(0, 3) * D_(93, 94), 耗时: 10.638s
[2025-07-24 04:49:38] y方向算符进度: 97/100, 当前算符: D_(0, 3) * D_(94, 97), 耗时: 10.594s
[2025-07-24 04:49:49] y方向算符进度: 98/100, 当前算符: D_(0, 3) * D_(95, 96), 耗时: 10.620s
[2025-07-24 04:49:59] y方向算符进度: 99/100, 当前算符: D_(0, 3) * D_(96, 99), 耗时: 10.580s
[2025-07-24 04:50:10] y方向算符进度: 100/100, 当前算符: D_(0, 3) * D_(97, 98), 耗时: 10.586s
[2025-07-24 04:50:10] y方向二聚体相关函数计算完成,耗时: 1052.12 秒
[2025-07-24 04:50:10] 计算傅里叶变换...
[2025-07-24 04:50:11] 二聚体结构因子计算完成
[2025-07-24 04:50:11] 二聚体相关函数平均误差: 0.000527
[2025-07-24 04:50:11] 恢复原始样本数: 4096
[2025-07-24 04:50:11] ================================================================================
[2025-07-24 04:50:11] 所有分析完成
