[2025-07-23 23:52:33] ================================================================================
[2025-07-23 23:52:33] 加载量子态: L=5, J2=0.00, J1=0.03
[2025-07-23 23:52:33] 设置样本数为: 1048576
[2025-07-23 23:52:33] 开始生成共享样本集...
[2025-07-23 23:55:53] 样本生成完成,耗时: 199.831 秒
[2025-07-23 23:55:53] ================================================================================
[2025-07-23 23:55:53] 开始计算自旋结构因子...
[2025-07-23 23:55:53] 初始化操作符缓存...
[2025-07-23 23:55:53] 预构建所有自旋相关操作符...
[2025-07-23 23:55:53] 开始计算自旋相关函数...
[2025-07-23 23:56:04] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 10.479s
[2025-07-23 23:56:16] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 12.138s
[2025-07-23 23:56:22] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.321s
[2025-07-23 23:56:29] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.314s
[2025-07-23 23:56:35] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.292s
[2025-07-23 23:56:41] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.307s
[2025-07-23 23:56:47] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.314s
[2025-07-23 23:56:54] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.291s
[2025-07-23 23:57:00] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.307s
[2025-07-23 23:57:06] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.292s
[2025-07-23 23:57:13] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.306s
[2025-07-23 23:57:19] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.291s
[2025-07-23 23:57:25] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.318s
[2025-07-23 23:57:32] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.318s
[2025-07-23 23:57:38] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.321s
[2025-07-23 23:57:44] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.291s
[2025-07-23 23:57:50] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.307s
[2025-07-23 23:57:57] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.289s
[2025-07-23 23:58:03] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.321s
[2025-07-23 23:58:09] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.321s
[2025-07-23 23:58:16] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.289s
[2025-07-23 23:58:22] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.290s
[2025-07-23 23:58:28] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.308s
[2025-07-23 23:58:35] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.319s
[2025-07-23 23:58:41] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.321s
[2025-07-23 23:58:47] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.317s
[2025-07-23 23:58:54] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.289s
[2025-07-23 23:59:00] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.307s
[2025-07-23 23:59:06] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.291s
[2025-07-23 23:59:12] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.314s
[2025-07-23 23:59:19] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.319s
[2025-07-23 23:59:25] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.404s
[2025-07-23 23:59:31] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.291s
[2025-07-23 23:59:38] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.289s
[2025-07-23 23:59:44] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.306s
[2025-07-23 23:59:50] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.290s
[2025-07-23 23:59:57] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.307s
[2025-07-24 00:00:03] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.307s
[2025-07-24 00:00:09] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.320s
[2025-07-24 00:00:16] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.320s
[2025-07-24 00:00:22] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.320s
[2025-07-24 00:00:28] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.319s
[2025-07-24 00:00:35] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.316s
[2025-07-24 00:00:41] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.321s
[2025-07-24 00:00:47] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.307s
[2025-07-24 00:00:54] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.307s
[2025-07-24 00:01:00] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.318s
[2025-07-24 00:01:06] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.318s
[2025-07-24 00:01:12] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.319s
[2025-07-24 00:01:19] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.289s
[2025-07-24 00:01:25] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.307s
[2025-07-24 00:01:31] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.386s
[2025-07-24 00:01:38] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.319s
[2025-07-24 00:01:44] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.311s
[2025-07-24 00:01:50] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.306s
[2025-07-24 00:01:57] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.291s
[2025-07-24 00:02:03] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.305s
[2025-07-24 00:02:09] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.319s
[2025-07-24 00:02:16] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.319s
[2025-07-24 00:02:22] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.317s
[2025-07-24 00:02:28] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.289s
[2025-07-24 00:02:35] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.292s
[2025-07-24 00:02:41] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.307s
[2025-07-24 00:02:47] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.319s
[2025-07-24 00:02:54] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.319s
[2025-07-24 00:03:00] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.312s
[2025-07-24 00:03:06] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.289s
[2025-07-24 00:03:12] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.306s
[2025-07-24 00:03:19] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.319s
[2025-07-24 00:03:25] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.319s
[2025-07-24 00:03:31] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.314s
[2025-07-24 00:03:38] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.290s
[2025-07-24 00:03:44] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.306s
[2025-07-24 00:03:50] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.305s
[2025-07-24 00:03:57] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.317s
[2025-07-24 00:04:03] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.320s
[2025-07-24 00:04:09] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.290s
[2025-07-24 00:04:15] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.290s
[2025-07-24 00:04:22] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.311s
[2025-07-24 00:04:28] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.319s
[2025-07-24 00:04:34] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.320s
[2025-07-24 00:04:41] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.307s
[2025-07-24 00:04:47] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.291s
[2025-07-24 00:04:53] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.307s
[2025-07-24 00:05:00] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.318s
[2025-07-24 00:05:06] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.321s
[2025-07-24 00:05:12] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.319s
[2025-07-24 00:05:19] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.307s
[2025-07-24 00:05:25] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.290s
[2025-07-24 00:05:31] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.319s
[2025-07-24 00:05:38] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.321s
[2025-07-24 00:05:44] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.321s
[2025-07-24 00:05:50] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.312s
[2025-07-24 00:05:57] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.310s
[2025-07-24 00:06:03] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.290s
[2025-07-24 00:06:09] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.312s
[2025-07-24 00:06:15] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.320s
[2025-07-24 00:06:22] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.318s
[2025-07-24 00:06:28] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.313s
[2025-07-24 00:06:34] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.291s
[2025-07-24 00:06:34] 自旋相关函数计算完成,总耗时 641.12 秒
[2025-07-24 00:06:35] 计算傅里叶变换...
[2025-07-24 00:06:35] 自旋结构因子计算完成
[2025-07-24 00:06:36] 自旋相关函数平均误差: 0.000739
[2025-07-24 00:06:36] ================================================================================
[2025-07-24 00:06:36] 开始计算二聚体结构因子...
[2025-07-24 00:06:36] 识别x和y方向的二聚体...
[2025-07-24 00:06:36] 找到 100 个x方向二聚体和 100 个y方向二聚体
[2025-07-24 00:06:36] 预计算二聚体操作符...
[2025-07-24 00:06:37] 使用x-二聚体 (0, 1) (全局索引 0) 作为参考点, 计算x方向连通二聚体相关函数...
[2025-07-24 00:06:44] x方向算符进度: 1/100, 当前算符: D_(0, 1) * D_(0, 1), 耗时: 6.342s
[2025-07-24 00:07:03] x方向算符进度: 2/100, 当前算符: D_(0, 1) * D_(0, 81), 耗时: 19.014s
[2025-07-24 00:07:11] x方向算符进度: 3/100, 当前算符: D_(0, 1) * D_(1, 20), 耗时: 8.845s
[2025-07-24 00:07:28] x方向算符进度: 4/100, 当前算符: D_(0, 1) * D_(2, 3), 耗时: 16.321s
[2025-07-24 00:07:38] x方向算符进度: 5/100, 当前算符: D_(0, 1) * D_(2, 23), 耗时: 10.562s
[2025-07-24 00:07:49] x方向算符进度: 6/100, 当前算符: D_(0, 1) * D_(3, 82), 耗时: 10.609s
[2025-07-24 00:08:00] x方向算符进度: 7/100, 当前算符: D_(0, 1) * D_(4, 5), 耗时: 10.563s
[2025-07-24 00:08:10] x方向算符进度: 8/100, 当前算符: D_(0, 1) * D_(4, 85), 耗时: 10.603s
[2025-07-24 00:08:21] x方向算符进度: 9/100, 当前算符: D_(0, 1) * D_(5, 24), 耗时: 10.602s
[2025-07-24 00:08:31] x方向算符进度: 10/100, 当前算符: D_(0, 1) * D_(6, 7), 耗时: 10.569s
[2025-07-24 00:08:42] x方向算符进度: 11/100, 当前算符: D_(0, 1) * D_(6, 27), 耗时: 10.604s
[2025-07-24 00:08:53] x方向算符进度: 12/100, 当前算符: D_(0, 1) * D_(7, 86), 耗时: 10.602s
[2025-07-24 00:09:03] x方向算符进度: 13/100, 当前算符: D_(0, 1) * D_(8, 9), 耗时: 10.621s
[2025-07-24 00:09:14] x方向算符进度: 14/100, 当前算符: D_(0, 1) * D_(8, 89), 耗时: 10.623s
[2025-07-24 00:09:24] x方向算符进度: 15/100, 当前算符: D_(0, 1) * D_(9, 28), 耗时: 10.567s
[2025-07-24 00:09:35] x方向算符进度: 16/100, 当前算符: D_(0, 1) * D_(10, 11), 耗时: 10.601s
[2025-07-24 00:09:45] x方向算符进度: 17/100, 当前算符: D_(0, 1) * D_(10, 31), 耗时: 10.564s
[2025-07-24 00:09:56] x方向算符进度: 18/100, 当前算符: D_(0, 1) * D_(11, 90), 耗时: 10.618s
[2025-07-24 00:10:07] x方向算符进度: 19/100, 当前算符: D_(0, 1) * D_(12, 13), 耗时: 10.617s
[2025-07-24 00:10:17] x方向算符进度: 20/100, 当前算符: D_(0, 1) * D_(12, 93), 耗时: 10.602s
[2025-07-24 00:10:28] x方向算符进度: 21/100, 当前算符: D_(0, 1) * D_(13, 32), 耗时: 10.610s
[2025-07-24 00:10:39] x方向算符进度: 22/100, 当前算符: D_(0, 1) * D_(14, 15), 耗时: 10.568s
[2025-07-24 00:10:49] x方向算符进度: 23/100, 当前算符: D_(0, 1) * D_(14, 35), 耗时: 10.621s
[2025-07-24 00:11:00] x方向算符进度: 24/100, 当前算符: D_(0, 1) * D_(15, 94), 耗时: 10.618s
[2025-07-24 00:11:10] x方向算符进度: 25/100, 当前算符: D_(0, 1) * D_(16, 17), 耗时: 10.620s
[2025-07-24 00:11:21] x方向算符进度: 26/100, 当前算符: D_(0, 1) * D_(16, 97), 耗时: 10.606s
[2025-07-24 00:11:32] x方向算符进度: 27/100, 当前算符: D_(0, 1) * D_(17, 36), 耗时: 10.565s
[2025-07-24 00:11:42] x方向算符进度: 28/100, 当前算符: D_(0, 1) * D_(18, 19), 耗时: 10.600s
[2025-07-24 00:11:53] x方向算符进度: 29/100, 当前算符: D_(0, 1) * D_(18, 39), 耗时: 10.616s
[2025-07-24 00:12:03] x方向算符进度: 30/100, 当前算符: D_(0, 1) * D_(19, 98), 耗时: 10.618s
[2025-07-24 00:12:14] x方向算符进度: 31/100, 当前算符: D_(0, 1) * D_(20, 21), 耗时: 10.615s
[2025-07-24 00:12:25] x方向算符进度: 32/100, 当前算符: D_(0, 1) * D_(21, 40), 耗时: 10.562s
[2025-07-24 00:12:35] x方向算符进度: 33/100, 当前算符: D_(0, 1) * D_(22, 23), 耗时: 10.602s
[2025-07-24 00:12:46] x方向算符进度: 34/100, 当前算符: D_(0, 1) * D_(22, 43), 耗时: 10.565s
[2025-07-24 00:12:56] x方向算符进度: 35/100, 当前算符: D_(0, 1) * D_(24, 25), 耗时: 10.622s
[2025-07-24 00:13:07] x方向算符进度: 36/100, 当前算符: D_(0, 1) * D_(25, 44), 耗时: 10.617s
[2025-07-24 00:13:18] x方向算符进度: 37/100, 当前算符: D_(0, 1) * D_(26, 27), 耗时: 10.605s
[2025-07-24 00:13:28] x方向算符进度: 38/100, 当前算符: D_(0, 1) * D_(26, 47), 耗时: 10.568s
[2025-07-24 00:13:39] x方向算符进度: 39/100, 当前算符: D_(0, 1) * D_(28, 29), 耗时: 10.618s
[2025-07-24 00:13:49] x方向算符进度: 40/100, 当前算符: D_(0, 1) * D_(29, 48), 耗时: 10.622s
[2025-07-24 00:14:00] x方向算符进度: 41/100, 当前算符: D_(0, 1) * D_(30, 31), 耗时: 10.616s
[2025-07-24 00:14:11] x方向算符进度: 42/100, 当前算符: D_(0, 1) * D_(30, 51), 耗时: 10.621s
[2025-07-24 00:14:21] x方向算符进度: 43/100, 当前算符: D_(0, 1) * D_(32, 33), 耗时: 10.619s
[2025-07-24 00:14:32] x方向算符进度: 44/100, 当前算符: D_(0, 1) * D_(33, 52), 耗时: 10.601s
[2025-07-24 00:14:42] x方向算符进度: 45/100, 当前算符: D_(0, 1) * D_(34, 35), 耗时: 10.565s
[2025-07-24 00:14:53] x方向算符进度: 46/100, 当前算符: D_(0, 1) * D_(34, 55), 耗时: 10.603s
[2025-07-24 00:15:04] x方向算符进度: 47/100, 当前算符: D_(0, 1) * D_(36, 37), 耗时: 10.617s
[2025-07-24 00:15:14] x方向算符进度: 48/100, 当前算符: D_(0, 1) * D_(37, 56), 耗时: 10.615s
[2025-07-24 00:15:25] x方向算符进度: 49/100, 当前算符: D_(0, 1) * D_(38, 39), 耗时: 10.601s
[2025-07-24 00:15:35] x方向算符进度: 50/100, 当前算符: D_(0, 1) * D_(38, 59), 耗时: 10.566s
[2025-07-24 00:15:46] x方向算符进度: 51/100, 当前算符: D_(0, 1) * D_(40, 41), 耗时: 10.601s
[2025-07-24 00:15:57] x方向算符进度: 52/100, 当前算符: D_(0, 1) * D_(41, 60), 耗时: 10.621s
[2025-07-24 00:16:07] x方向算符进度: 53/100, 当前算符: D_(0, 1) * D_(42, 43), 耗时: 10.621s
[2025-07-24 00:16:18] x方向算符进度: 54/100, 当前算符: D_(0, 1) * D_(42, 63), 耗时: 10.609s
[2025-07-24 00:16:28] x方向算符进度: 55/100, 当前算符: D_(0, 1) * D_(44, 45), 耗时: 10.604s
[2025-07-24 00:16:39] x方向算符进度: 56/100, 当前算符: D_(0, 1) * D_(45, 64), 耗时: 10.562s
[2025-07-24 00:16:50] x方向算符进度: 57/100, 当前算符: D_(0, 1) * D_(46, 47), 耗时: 10.607s
[2025-07-24 00:17:00] x方向算符进度: 58/100, 当前算符: D_(0, 1) * D_(46, 67), 耗时: 10.620s
[2025-07-24 00:17:11] x方向算符进度: 59/100, 当前算符: D_(0, 1) * D_(48, 49), 耗时: 10.618s
[2025-07-24 00:17:21] x方向算符进度: 60/100, 当前算符: D_(0, 1) * D_(49, 68), 耗时: 10.565s
[2025-07-24 00:17:32] x方向算符进度: 61/100, 当前算符: D_(0, 1) * D_(50, 51), 耗时: 10.602s
[2025-07-24 00:17:43] x方向算符进度: 62/100, 当前算符: D_(0, 1) * D_(50, 71), 耗时: 10.620s
[2025-07-24 00:17:53] x方向算符进度: 63/100, 当前算符: D_(0, 1) * D_(52, 53), 耗时: 10.617s
[2025-07-24 00:18:04] x方向算符进度: 64/100, 当前算符: D_(0, 1) * D_(53, 72), 耗时: 10.615s
[2025-07-24 00:18:14] x方向算符进度: 65/100, 当前算符: D_(0, 1) * D_(54, 55), 耗时: 10.560s
[2025-07-24 00:18:25] x方向算符进度: 66/100, 当前算符: D_(0, 1) * D_(54, 75), 耗时: 10.702s
[2025-07-24 00:18:36] x方向算符进度: 67/100, 当前算符: D_(0, 1) * D_(56, 57), 耗时: 10.565s
[2025-07-24 00:18:46] x方向算符进度: 68/100, 当前算符: D_(0, 1) * D_(57, 76), 耗时: 10.622s
[2025-07-24 00:18:57] x方向算符进度: 69/100, 当前算符: D_(0, 1) * D_(58, 59), 耗时: 10.621s
[2025-07-24 00:19:08] x方向算符进度: 70/100, 当前算符: D_(0, 1) * D_(58, 79), 耗时: 10.563s
[2025-07-24 00:19:18] x方向算符进度: 71/100, 当前算符: D_(0, 1) * D_(60, 61), 耗时: 10.603s
[2025-07-24 00:19:29] x方向算符进度: 72/100, 当前算符: D_(0, 1) * D_(61, 80), 耗时: 10.561s
[2025-07-24 00:19:39] x方向算符进度: 73/100, 当前算符: D_(0, 1) * D_(62, 63), 耗时: 10.617s
[2025-07-24 00:19:50] x方向算符进度: 74/100, 当前算符: D_(0, 1) * D_(62, 83), 耗时: 10.617s
[2025-07-24 00:20:01] x方向算符进度: 75/100, 当前算符: D_(0, 1) * D_(64, 65), 耗时: 10.602s
[2025-07-24 00:20:11] x方向算符进度: 76/100, 当前算符: D_(0, 1) * D_(65, 84), 耗时: 10.567s
[2025-07-24 00:20:22] x方向算符进度: 77/100, 当前算符: D_(0, 1) * D_(66, 67), 耗时: 10.599s
[2025-07-24 00:20:32] x方向算符进度: 78/100, 当前算符: D_(0, 1) * D_(66, 87), 耗时: 10.565s
[2025-07-24 00:20:43] x方向算符进度: 79/100, 当前算符: D_(0, 1) * D_(68, 69), 耗时: 10.564s
[2025-07-24 00:20:54] x方向算符进度: 80/100, 当前算符: D_(0, 1) * D_(69, 88), 耗时: 10.621s
[2025-07-24 00:21:04] x方向算符进度: 81/100, 当前算符: D_(0, 1) * D_(70, 71), 耗时: 10.620s
[2025-07-24 00:21:15] x方向算符进度: 82/100, 当前算符: D_(0, 1) * D_(70, 91), 耗时: 10.606s
[2025-07-24 00:21:25] x方向算符进度: 83/100, 当前算符: D_(0, 1) * D_(72, 73), 耗时: 10.562s
[2025-07-24 00:21:36] x方向算符进度: 84/100, 当前算符: D_(0, 1) * D_(73, 92), 耗时: 10.604s
[2025-07-24 00:21:47] x方向算符进度: 85/100, 当前算符: D_(0, 1) * D_(74, 75), 耗时: 10.623s
[2025-07-24 00:21:57] x方向算符进度: 86/100, 当前算符: D_(0, 1) * D_(74, 95), 耗时: 10.619s
[2025-07-24 00:22:08] x方向算符进度: 87/100, 当前算符: D_(0, 1) * D_(76, 77), 耗时: 10.611s
[2025-07-24 00:22:18] x方向算符进度: 88/100, 当前算符: D_(0, 1) * D_(77, 96), 耗时: 10.565s
[2025-07-24 00:22:29] x方向算符进度: 89/100, 当前算符: D_(0, 1) * D_(78, 79), 耗时: 10.602s
[2025-07-24 00:22:39] x方向算符进度: 90/100, 当前算符: D_(0, 1) * D_(78, 99), 耗时: 10.562s
[2025-07-24 00:22:50] x方向算符进度: 91/100, 当前算符: D_(0, 1) * D_(80, 81), 耗时: 10.616s
[2025-07-24 00:23:01] x方向算符进度: 92/100, 当前算符: D_(0, 1) * D_(82, 83), 耗时: 10.622s
[2025-07-24 00:23:11] x方向算符进度: 93/100, 当前算符: D_(0, 1) * D_(84, 85), 耗时: 10.619s
[2025-07-24 00:23:22] x方向算符进度: 94/100, 当前算符: D_(0, 1) * D_(86, 87), 耗时: 10.606s
[2025-07-24 00:23:33] x方向算符进度: 95/100, 当前算符: D_(0, 1) * D_(88, 89), 耗时: 10.568s
[2025-07-24 00:23:43] x方向算符进度: 96/100, 当前算符: D_(0, 1) * D_(90, 91), 耗时: 10.601s
[2025-07-24 00:23:54] x方向算符进度: 97/100, 当前算符: D_(0, 1) * D_(92, 93), 耗时: 10.565s
[2025-07-24 00:24:04] x方向算符进度: 98/100, 当前算符: D_(0, 1) * D_(94, 95), 耗时: 10.621s
[2025-07-24 00:24:15] x方向算符进度: 99/100, 当前算符: D_(0, 1) * D_(96, 97), 耗时: 10.622s
[2025-07-24 00:24:26] x方向算符进度: 100/100, 当前算符: D_(0, 1) * D_(98, 99), 耗时: 10.608s
[2025-07-24 00:24:26] x方向二聚体相关函数计算完成,耗时: 1068.30 秒
[2025-07-24 00:24:26] --------------------------------------------------------------------------------
[2025-07-24 00:24:26] 使用y-二聚体 (0, 3) (全局索引 100) 作为参考点, 计算y方向连通二聚体相关函数...
[2025-07-24 00:24:32] y方向算符进度: 1/100, 当前算符: D_(0, 3) * D_(0, 3), 耗时: 6.298s
[2025-07-24 00:24:41] y方向算符进度: 2/100, 当前算符: D_(0, 3) * D_(0, 19), 耗时: 8.833s
[2025-07-24 00:24:51] y方向算符进度: 3/100, 当前算符: D_(0, 3) * D_(1, 2), 耗时: 10.626s
[2025-07-24 00:25:02] y方向算符进度: 4/100, 当前算符: D_(0, 3) * D_(1, 18), 耗时: 10.563s
[2025-07-24 00:25:12] y方向算符进度: 5/100, 当前算符: D_(0, 3) * D_(2, 5), 耗时: 10.562s
[2025-07-24 00:25:21] y方向算符进度: 6/100, 当前算符: D_(0, 3) * D_(3, 4), 耗时: 8.829s
[2025-07-24 00:25:32] y方向算符进度: 7/100, 当前算符: D_(0, 3) * D_(4, 7), 耗时: 10.612s
[2025-07-24 00:25:43] y方向算符进度: 8/100, 当前算符: D_(0, 3) * D_(5, 6), 耗时: 10.628s
[2025-07-24 00:25:53] y方向算符进度: 9/100, 当前算符: D_(0, 3) * D_(6, 9), 耗时: 10.565s
[2025-07-24 00:26:04] y方向算符进度: 10/100, 当前算符: D_(0, 3) * D_(7, 8), 耗时: 10.628s
[2025-07-24 00:26:14] y方向算符进度: 11/100, 当前算符: D_(0, 3) * D_(8, 11), 耗时: 10.565s
[2025-07-24 00:26:25] y方向算符进度: 12/100, 当前算符: D_(0, 3) * D_(9, 10), 耗时: 10.626s
[2025-07-24 00:26:35] y方向算符进度: 13/100, 当前算符: D_(0, 3) * D_(10, 13), 耗时: 10.563s
[2025-07-24 00:26:46] y方向算符进度: 14/100, 当前算符: D_(0, 3) * D_(11, 12), 耗时: 10.628s
[2025-07-24 00:26:57] y方向算符进度: 15/100, 当前算符: D_(0, 3) * D_(12, 15), 耗时: 10.620s
[2025-07-24 00:27:07] y方向算符进度: 16/100, 当前算符: D_(0, 3) * D_(13, 14), 耗时: 10.626s
[2025-07-24 00:27:18] y方向算符进度: 17/100, 当前算符: D_(0, 3) * D_(14, 17), 耗时: 10.564s
[2025-07-24 00:27:28] y方向算符进度: 18/100, 当前算符: D_(0, 3) * D_(15, 16), 耗时: 10.568s
[2025-07-24 00:27:39] y方向算符进度: 19/100, 当前算符: D_(0, 3) * D_(16, 19), 耗时: 10.633s
[2025-07-24 00:27:50] y方向算符进度: 20/100, 当前算符: D_(0, 3) * D_(17, 18), 耗时: 10.619s
[2025-07-24 00:28:00] y方向算符进度: 21/100, 当前算符: D_(0, 3) * D_(20, 23), 耗时: 10.627s
[2025-07-24 00:28:11] y方向算符进度: 22/100, 当前算符: D_(0, 3) * D_(20, 39), 耗时: 10.565s
[2025-07-24 00:28:22] y方向算符进度: 23/100, 当前算符: D_(0, 3) * D_(21, 22), 耗时: 10.629s
[2025-07-24 00:28:32] y方向算符进度: 24/100, 当前算符: D_(0, 3) * D_(21, 38), 耗时: 10.653s
[2025-07-24 00:28:43] y方向算符进度: 25/100, 当前算符: D_(0, 3) * D_(22, 25), 耗时: 10.626s
[2025-07-24 00:28:53] y方向算符进度: 26/100, 当前算符: D_(0, 3) * D_(23, 24), 耗时: 10.615s
[2025-07-24 00:29:04] y方向算符进度: 27/100, 当前算符: D_(0, 3) * D_(24, 27), 耗时: 10.631s
[2025-07-24 00:29:15] y方向算符进度: 28/100, 当前算符: D_(0, 3) * D_(25, 26), 耗时: 10.620s
[2025-07-24 00:29:25] y方向算符进度: 29/100, 当前算符: D_(0, 3) * D_(26, 29), 耗时: 10.636s
[2025-07-24 00:29:36] y方向算符进度: 30/100, 当前算符: D_(0, 3) * D_(27, 28), 耗时: 10.625s
[2025-07-24 00:29:47] y方向算符进度: 31/100, 当前算符: D_(0, 3) * D_(28, 31), 耗时: 10.564s
[2025-07-24 00:29:57] y方向算符进度: 32/100, 当前算符: D_(0, 3) * D_(29, 30), 耗时: 10.628s
[2025-07-24 00:30:08] y方向算符进度: 33/100, 当前算符: D_(0, 3) * D_(30, 33), 耗时: 10.619s
[2025-07-24 00:30:18] y方向算符进度: 34/100, 当前算符: D_(0, 3) * D_(31, 32), 耗时: 10.614s
[2025-07-24 00:30:29] y方向算符进度: 35/100, 当前算符: D_(0, 3) * D_(32, 35), 耗时: 10.625s
[2025-07-24 00:30:40] y方向算符进度: 36/100, 当前算符: D_(0, 3) * D_(33, 34), 耗时: 10.564s
[2025-07-24 00:30:50] y方向算符进度: 37/100, 当前算符: D_(0, 3) * D_(34, 37), 耗时: 10.628s
[2025-07-24 00:31:01] y方向算符进度: 38/100, 当前算符: D_(0, 3) * D_(35, 36), 耗时: 10.619s
[2025-07-24 00:31:11] y方向算符进度: 39/100, 当前算符: D_(0, 3) * D_(36, 39), 耗时: 10.631s
[2025-07-24 00:31:22] y方向算符进度: 40/100, 当前算符: D_(0, 3) * D_(37, 38), 耗时: 10.565s
[2025-07-24 00:31:33] y方向算符进度: 41/100, 当前算符: D_(0, 3) * D_(40, 43), 耗时: 10.631s
[2025-07-24 00:31:43] y方向算符进度: 42/100, 当前算符: D_(0, 3) * D_(40, 59), 耗时: 10.564s
[2025-07-24 00:31:54] y方向算符进度: 43/100, 当前算符: D_(0, 3) * D_(41, 42), 耗时: 10.629s
[2025-07-24 00:32:04] y方向算符进度: 44/100, 当前算符: D_(0, 3) * D_(41, 58), 耗时: 10.620s
[2025-07-24 00:32:15] y方向算符进度: 45/100, 当前算符: D_(0, 3) * D_(42, 45), 耗时: 10.625s
[2025-07-24 00:32:26] y方向算符进度: 46/100, 当前算符: D_(0, 3) * D_(43, 44), 耗时: 10.627s
[2025-07-24 00:32:36] y方向算符进度: 47/100, 当前算符: D_(0, 3) * D_(44, 47), 耗时: 10.564s
[2025-07-24 00:32:47] y方向算符进度: 48/100, 当前算符: D_(0, 3) * D_(45, 46), 耗时: 10.629s
[2025-07-24 00:32:58] y方向算符进度: 49/100, 当前算符: D_(0, 3) * D_(46, 49), 耗时: 10.564s
[2025-07-24 00:33:08] y方向算符进度: 50/100, 当前算符: D_(0, 3) * D_(47, 48), 耗时: 10.624s
[2025-07-24 00:33:19] y方向算符进度: 51/100, 当前算符: D_(0, 3) * D_(48, 51), 耗时: 10.627s
[2025-07-24 00:33:29] y方向算符进度: 52/100, 当前算符: D_(0, 3) * D_(49, 50), 耗时: 10.617s
[2025-07-24 00:33:40] y方向算符进度: 53/100, 当前算符: D_(0, 3) * D_(50, 53), 耗时: 10.625s
[2025-07-24 00:33:51] y方向算符进度: 54/100, 当前算符: D_(0, 3) * D_(51, 52), 耗时: 10.565s
[2025-07-24 00:34:01] y方向算符进度: 55/100, 当前算符: D_(0, 3) * D_(52, 55), 耗时: 10.630s
[2025-07-24 00:34:12] y方向算符进度: 56/100, 当前算符: D_(0, 3) * D_(53, 54), 耗时: 10.597s
[2025-07-24 00:34:22] y方向算符进度: 57/100, 当前算符: D_(0, 3) * D_(54, 57), 耗时: 10.626s
[2025-07-24 00:34:33] y方向算符进度: 58/100, 当前算符: D_(0, 3) * D_(55, 56), 耗时: 10.619s
[2025-07-24 00:34:44] y方向算符进度: 59/100, 当前算符: D_(0, 3) * D_(56, 59), 耗时: 10.635s
[2025-07-24 00:34:54] y方向算符进度: 60/100, 当前算符: D_(0, 3) * D_(57, 58), 耗时: 10.563s
[2025-07-24 00:35:05] y方向算符进度: 61/100, 当前算符: D_(0, 3) * D_(60, 63), 耗时: 10.627s
[2025-07-24 00:35:16] y方向算符进度: 62/100, 当前算符: D_(0, 3) * D_(60, 79), 耗时: 10.629s
[2025-07-24 00:35:26] y方向算符进度: 63/100, 当前算符: D_(0, 3) * D_(61, 62), 耗时: 10.619s
[2025-07-24 00:35:37] y方向算符进度: 64/100, 当前算符: D_(0, 3) * D_(61, 78), 耗时: 10.632s
[2025-07-24 00:35:47] y方向算符进度: 65/100, 当前算符: D_(0, 3) * D_(62, 65), 耗时: 10.563s
[2025-07-24 00:35:58] y方向算符进度: 66/100, 当前算符: D_(0, 3) * D_(63, 64), 耗时: 10.628s
[2025-07-24 00:36:09] y方向算符进度: 67/100, 当前算符: D_(0, 3) * D_(64, 67), 耗时: 10.618s
[2025-07-24 00:36:19] y方向算符进度: 68/100, 当前算符: D_(0, 3) * D_(65, 66), 耗时: 10.616s
[2025-07-24 00:36:30] y方向算符进度: 69/100, 当前算符: D_(0, 3) * D_(66, 69), 耗时: 10.625s
[2025-07-24 00:36:40] y方向算符进度: 70/100, 当前算符: D_(0, 3) * D_(67, 68), 耗时: 10.564s
[2025-07-24 00:36:51] y方向算符进度: 71/100, 当前算符: D_(0, 3) * D_(68, 71), 耗时: 10.625s
[2025-07-24 00:37:02] y方向算符进度: 72/100, 当前算符: D_(0, 3) * D_(69, 70), 耗时: 10.667s
[2025-07-24 00:37:12] y方向算符进度: 73/100, 当前算符: D_(0, 3) * D_(70, 73), 耗时: 10.628s
[2025-07-24 00:37:23] y方向算符进度: 74/100, 当前算符: D_(0, 3) * D_(71, 72), 耗时: 10.619s
[2025-07-24 00:37:34] y方向算符进度: 75/100, 当前算符: D_(0, 3) * D_(72, 75), 耗时: 10.625s
[2025-07-24 00:37:44] y方向算符进度: 76/100, 当前算符: D_(0, 3) * D_(73, 74), 耗时: 10.566s
[2025-07-24 00:37:55] y方向算符进度: 77/100, 当前算符: D_(0, 3) * D_(74, 77), 耗时: 10.627s
[2025-07-24 00:38:05] y方向算符进度: 78/100, 当前算符: D_(0, 3) * D_(75, 76), 耗时: 10.627s
[2025-07-24 00:38:16] y方向算符进度: 79/100, 当前算符: D_(0, 3) * D_(76, 79), 耗时: 10.619s
[2025-07-24 00:38:27] y方向算符进度: 80/100, 当前算符: D_(0, 3) * D_(77, 78), 耗时: 10.624s
[2025-07-24 00:38:37] y方向算符进度: 81/100, 当前算符: D_(0, 3) * D_(80, 83), 耗时: 10.563s
[2025-07-24 00:38:48] y方向算符进度: 82/100, 当前算符: D_(0, 3) * D_(80, 99), 耗时: 10.628s
[2025-07-24 00:38:58] y方向算符进度: 83/100, 当前算符: D_(0, 3) * D_(81, 82), 耗时: 10.617s
[2025-07-24 00:39:09] y方向算符进度: 84/100, 当前算符: D_(0, 3) * D_(81, 98), 耗时: 10.627s
[2025-07-24 00:39:20] y方向算符进度: 85/100, 当前算符: D_(0, 3) * D_(82, 85), 耗时: 10.565s
[2025-07-24 00:39:30] y方向算符进度: 86/100, 当前算符: D_(0, 3) * D_(83, 84), 耗时: 10.628s
[2025-07-24 00:39:41] y方向算符进度: 87/100, 当前算符: D_(0, 3) * D_(84, 87), 耗时: 10.565s
[2025-07-24 00:39:51] y方向算符进度: 88/100, 当前算符: D_(0, 3) * D_(85, 86), 耗时: 10.625s
[2025-07-24 00:40:02] y方向算符进度: 89/100, 当前算符: D_(0, 3) * D_(86, 89), 耗时: 10.610s
[2025-07-24 00:40:13] y方向算符进度: 90/100, 当前算符: D_(0, 3) * D_(87, 88), 耗时: 10.636s
[2025-07-24 00:40:23] y方向算符进度: 91/100, 当前算符: D_(0, 3) * D_(88, 91), 耗时: 10.620s
[2025-07-24 00:40:34] y方向算符进度: 92/100, 当前算符: D_(0, 3) * D_(89, 90), 耗时: 10.629s
[2025-07-24 00:40:45] y方向算符进度: 93/100, 当前算符: D_(0, 3) * D_(90, 93), 耗时: 10.565s
[2025-07-24 00:40:55] y方向算符进度: 94/100, 当前算符: D_(0, 3) * D_(91, 92), 耗时: 10.566s
[2025-07-24 00:41:06] y方向算符进度: 95/100, 当前算符: D_(0, 3) * D_(92, 95), 耗时: 10.718s
[2025-07-24 00:41:16] y方向算符进度: 96/100, 当前算符: D_(0, 3) * D_(93, 94), 耗时: 10.603s
[2025-07-24 00:41:27] y方向算符进度: 97/100, 当前算符: D_(0, 3) * D_(94, 97), 耗时: 10.630s
[2025-07-24 00:41:38] y方向算符进度: 98/100, 当前算符: D_(0, 3) * D_(95, 96), 耗时: 10.620s
[2025-07-24 00:41:48] y方向算符进度: 99/100, 当前算符: D_(0, 3) * D_(96, 99), 耗时: 10.628s
[2025-07-24 00:41:59] y方向算符进度: 100/100, 当前算符: D_(0, 3) * D_(97, 98), 耗时: 10.563s
[2025-07-24 00:41:59] y方向二聚体相关函数计算完成,耗时: 1053.30 秒
[2025-07-24 00:41:59] 计算傅里叶变换...
[2025-07-24 00:42:00] 二聚体结构因子计算完成
[2025-07-24 00:42:01] 二聚体相关函数平均误差: 0.000566
[2025-07-24 00:42:01] 恢复原始样本数: 4096
[2025-07-24 00:42:01] ================================================================================
[2025-07-24 00:42:01] 所有分析完成
