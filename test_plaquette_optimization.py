#!/usr/bin/env python3
"""
测试简盘结构因子计算的优化效果
"""

import os
import sys
import time

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 设置环境变量
os.environ["XLA_FLAGS"] = "--xla_gpu_cuda_data_dir=/usr/local/cuda"
os.environ["NETKET_EXPERIMENTAL_SHARDING"] = "1"
os.environ["XLA_PYTHON_CLIENT_ALLOCATOR"] = "platform"
os.environ["XLA_PYTHON_CLIENT_PREALLOCATE"] = "false"
os.environ["JAX_PLATFORM_NAME"] = "gpu"

import numpy as np
import netket as nk
from src.models.gcnn import create_gcnn_model
from src.analysis.structure_factors import (
    calculate_plaquette_structure_factor,
    batch_expect_with_shared_samples,
    batch_expect_chunked,
    batch_expect_parallel_chunks
)

def test_optimization_comparison():
    """
    比较不同批量计算方法的性能
    """
    print("=" * 80)
    print("简盘结构因子计算优化测试")
    print("=" * 80)
    
    # 创建小规模测试系统
    L = 2  # 使用小系统进行快速测试
    J2 = 1.0
    J1 = 0.74
    
    print(f"创建测试系统: L={L}, J2={J2}, J1={J1}")
    
    # 创建模型和量子态
    vqs, lattice, hilbert, hamiltonian = create_gcnn_model(
        L=L, J2=J2, J1=J1,
        n_samples=1000,  # 减少样本数以加快测试
        n_discard=50,
        chunk_size=None
    )
    
    # 加载预训练参数（如果存在）
    params_file = f"results/L={L}/J2={J2:.2f}/J1={J1:.2f}/quantum_state.pkl"
    if os.path.exists(params_file):
        print(f"加载预训练参数: {params_file}")
        import pickle
        with open(params_file, "rb") as f:
            vqs.parameters = pickle.load(f)
    else:
        print("使用随机初始化参数")
    
    # 创建测试目录
    test_dir = "test_optimization_results"
    os.makedirs(test_dir, exist_ok=True)
    
    print("\n开始测试简盘结构因子计算...")
    
    # 测试原始方法（使用优化后的批量方法）
    start_time = time.time()
    try:
        k_points, (plaq_sf_real, plaq_sf_imag) = calculate_plaquette_structure_factor(
            vqs, lattice, L, test_dir
        )
        original_time = time.time() - start_time
        print(f"✓ 优化后的批量计算完成，耗时: {original_time:.2f} 秒")
        
        # 显示结果统计
        print(f"  - 结构因子实部范围: [{np.min(plaq_sf_real):.6f}, {np.max(plaq_sf_real):.6f}]")
        print(f"  - 结构因子虚部范围: [{np.min(plaq_sf_imag):.6f}, {np.max(plaq_sf_imag):.6f}]")
        
    except Exception as e:
        print(f"✗ 优化后的批量计算失败: {e}")
        return
    
    print("\n" + "=" * 80)
    print("测试完成！")
    print("=" * 80)
    
    # 清理测试文件
    import shutil
    if os.path.exists(test_dir):
        shutil.rmtree(test_dir)
        print("清理测试文件完成")

def test_batch_methods():
    """
    测试不同的批量计算方法
    """
    print("\n" + "=" * 80)
    print("批量计算方法性能测试")
    print("=" * 80)
    
    # 创建简单的测试算符
    L = 2
    hilbert = nk.hilbert.Spin(s=0.5, N=4*L*L)
    
    # 创建简单的变分态
    model = nk.models.RBM(alpha=1)
    sampler = nk.sampler.MetropolisLocal(hilbert, n_chains=16)
    vqs = nk.vqs.MCState(sampler, model, n_samples=500)
    
    # 创建测试算符列表
    operators = []
    for i in range(16):  # 创建16个简单算符
        op = nk.operator.spin.sigmaz(hilbert, i % hilbert.size)
        operators.append(op.to_jax_operator())
    
    print(f"创建了 {len(operators)} 个测试算符")
    
    # 测试不同的批量方法
    methods = [
        ("标准批量方法", lambda: batch_expect_with_shared_samples(vqs, operators)),
        ("分块方法(chunk=8)", lambda: batch_expect_chunked(vqs, operators, chunk_size=8)),
        ("并行分块方法(chunk=4)", lambda: batch_expect_parallel_chunks(vqs, operators, chunk_size=4)),
    ]
    
    results = {}
    
    for method_name, method_func in methods:
        print(f"\n测试 {method_name}...")
        try:
            start_time = time.time()
            result = method_func()
            elapsed_time = time.time() - start_time
            
            print(f"✓ {method_name} 完成，耗时: {elapsed_time:.3f} 秒")
            print(f"  - 返回结果数量: {len(result)}")
            
            results[method_name] = {
                'time': elapsed_time,
                'results': result
            }
            
        except Exception as e:
            print(f"✗ {method_name} 失败: {e}")
    
    # 比较结果
    if len(results) > 1:
        print(f"\n性能比较:")
        times = [(name, data['time']) for name, data in results.items()]
        times.sort(key=lambda x: x[1])
        
        fastest_time = times[0][1]
        for name, time_taken in times:
            speedup = fastest_time / time_taken if time_taken > 0 else 1.0
            print(f"  {name}: {time_taken:.3f}s (相对最快: {speedup:.2f}x)")

if __name__ == "__main__":
    print("开始简盘结构因子优化测试...")
    
    try:
        test_optimization_comparison()
        test_batch_methods()
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n测试程序结束")
