Analysis job start at: Thu Jul 24 21:22:04 +08 2025
Running on node: x1000c0s6b0n1
GPU Information:
Thu Jul 24 21:22:04 2025       
+---------------------------------------------------------------------------------------+
| NVIDIA-SMI 535.154.05             Driver Version: 535.154.05   CUDA Version: 12.2     |
|-----------------------------------------+----------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id        Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |         Memory-Usage | GPU-Util  Compute M. |
|                                         |                      |               MIG M. |
|=========================================+======================+======================|
|   0  NVIDIA A100-SXM4-40GB          On  | 00000000:03:00.0 Off |                    0 |
| N/A   45C    P0              52W / 400W |      0MiB / 40960MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
                                                                                         
+---------------------------------------------------------------------------------------+
| Processes:                                                                            |
|  GPU   GI   CI        PID   Type   Process name                            GPU Memory |
|        ID   ID                                                             Usage      |
|=======================================================================================|
|  No running processes found                                                           |
+---------------------------------------------------------------------------------------+
Starting analysis: L=4, J2=1.00, J1=0.74 at: Thu Jul 24 21:22:05 +08 2025
13:4: not a valid test operator: (
13:4: not a valid test operator: 535.154.05
2025-07-24 13:22:32.333064: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.
Fall back to parse the raw backend config str.
2025-07-24 13:22:32.333111: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.
Fall back to parse the raw backend config str.
2025-07-24 13:25:13.423307: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.
Fall back to parse the raw backend config str.
2025-07-24 13:25:13.423345: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.
Fall back to parse the raw backend config str.
2025-07-24 13:25:14.186803: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.
Fall back to parse the raw backend config str.
2025-07-24 13:25:14.186839: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.
Fall back to parse the raw backend config str.
2025-07-24 13:25:21.770552: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.
Fall back to parse the raw backend config str.
2025-07-24 13:25:21.770591: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.
Fall back to parse the raw backend config str.
2025-07-24 13:25:22.507598: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.
Fall back to parse the raw backend config str.
2025-07-24 13:25:22.507645: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.
Fall back to parse the raw backend config str.
2025-07-24 13:25:24.574993: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.
Fall back to parse the raw backend config str.
2025-07-24 13:25:24.575030: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.
Fall back to parse the raw backend config str.
2025-07-24 13:25:26.425579: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.
Fall back to parse the raw backend config str.
2025-07-24 13:25:26.425615: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.
Fall back to parse the raw backend config str.
[2025-07-24 21:22:39] ================================================================================
[2025-07-24 21:22:39] 加载量子态: L=4, J2=1.00, J1=0.74
[2025-07-24 21:22:39] 设置样本数为: 1048576
[2025-07-24 21:22:39] 开始生成共享样本集...
[2025-07-24 21:25:13] 样本生成完成,耗时: 153.329 秒
[2025-07-24 21:25:13] ================================================================================
[2025-07-24 21:25:13] 开始计算简盘结构因子...
[2025-07-24 21:25:13] k点网格生成耗时: 0.0002 秒
[2025-07-24 21:25:13] 识别所有可能的4节点简盘...
[2025-07-24 21:25:13] 找到 64 个有效的4节点简盘, 耗时: 0.0078 秒
[2025-07-24 21:25:13] 预计算简盘操作符 P_k 和 P_k^-1...
[2025-07-24 21:25:14]   第1/64个简盘操作符构建耗时: 1.4806 秒
[2025-07-24 21:25:14]   第2/64个简盘操作符构建耗时: 0.1036 秒
[2025-07-24 21:25:14]   第3/64个简盘操作符构建耗时: 0.1026 秒
[2025-07-24 21:25:14]   第4/64个简盘操作符构建耗时: 0.1220 秒
[2025-07-24 21:25:14]   第5/64个简盘操作符构建耗时: 0.0851 秒
[2025-07-24 21:25:15]   第6/64个简盘操作符构建耗时: 0.1038 秒
[2025-07-24 21:25:15]   第7/64个简盘操作符构建耗时: 0.1025 秒
[2025-07-24 21:25:15]   第8/64个简盘操作符构建耗时: 0.1211 秒
[2025-07-24 21:25:15]   第9/64个简盘操作符构建耗时: 0.0839 秒
[2025-07-24 21:25:15]   第10/64个简盘操作符构建耗时: 0.1023 秒
[2025-07-24 21:25:15]   第11/64个简盘操作符构建耗时: 0.1026 秒
[2025-07-24 21:25:15]   第12/64个简盘操作符构建耗时: 0.1212 秒
[2025-07-24 21:25:15]   第13/64个简盘操作符构建耗时: 0.0838 秒
[2025-07-24 21:25:15]   第14/64个简盘操作符构建耗时: 0.1027 秒
[2025-07-24 21:25:16]   第15/64个简盘操作符构建耗时: 0.1083 秒
[2025-07-24 21:25:16]   第16/64个简盘操作符构建耗时: 0.1203 秒
[2025-07-24 21:25:16]   第17/64个简盘操作符构建耗时: 0.0834 秒
[2025-07-24 21:25:16]   第18/64个简盘操作符构建耗时: 0.1023 秒
[2025-07-24 21:25:16]   第19/64个简盘操作符构建耗时: 0.1023 秒
[2025-07-24 21:25:16]   第20/64个简盘操作符构建耗时: 0.1209 秒
[2025-07-24 21:25:16]   第21/64个简盘操作符构建耗时: 0.0843 秒
[2025-07-24 21:25:16]   第22/64个简盘操作符构建耗时: 0.1031 秒
[2025-07-24 21:25:16]   第23/64个简盘操作符构建耗时: 0.1019 秒
[2025-07-24 21:25:16]   第24/64个简盘操作符构建耗时: 0.1203 秒
[2025-07-24 21:25:17]   第25/64个简盘操作符构建耗时: 0.0830 秒
[2025-07-24 21:25:17]   第26/64个简盘操作符构建耗时: 0.1018 秒
[2025-07-24 21:25:17]   第27/64个简盘操作符构建耗时: 0.1018 秒
[2025-07-24 21:25:17]   第28/64个简盘操作符构建耗时: 0.1201 秒
[2025-07-24 21:25:17]   第29/64个简盘操作符构建耗时: 0.0831 秒
[2025-07-24 21:25:17]   第30/64个简盘操作符构建耗时: 0.1020 秒
[2025-07-24 21:25:17]   第31/64个简盘操作符构建耗时: 0.1081 秒
[2025-07-24 21:25:17]   第32/64个简盘操作符构建耗时: 0.1208 秒
[2025-07-24 21:25:17]   第33/64个简盘操作符构建耗时: 0.0836 秒
[2025-07-24 21:25:17]   第34/64个简盘操作符构建耗时: 0.1020 秒
[2025-07-24 21:25:18]   第35/64个简盘操作符构建耗时: 0.1015 秒
[2025-07-24 21:25:18]   第36/64个简盘操作符构建耗时: 0.1200 秒
[2025-07-24 21:25:18]   第37/64个简盘操作符构建耗时: 0.0833 秒
[2025-07-24 21:25:18]   第38/64个简盘操作符构建耗时: 0.1025 秒
[2025-07-24 21:25:18]   第39/64个简盘操作符构建耗时: 0.1027 秒
[2025-07-24 21:25:18]   第40/64个简盘操作符构建耗时: 0.1201 秒
[2025-07-24 21:25:18]   第41/64个简盘操作符构建耗时: 0.0831 秒
[2025-07-24 21:25:18]   第42/64个简盘操作符构建耗时: 0.1019 秒
[2025-07-24 21:25:18]   第43/64个简盘操作符构建耗时: 0.1017 秒
[2025-07-24 21:25:19]   第44/64个简盘操作符构建耗时: 0.1205 秒
[2025-07-24 21:25:19]   第45/64个简盘操作符构建耗时: 0.0838 秒
[2025-07-24 21:25:19]   第46/64个简盘操作符构建耗时: 0.1026 秒
[2025-07-24 21:25:19]   第47/64个简盘操作符构建耗时: 0.1091 秒
[2025-07-24 21:25:19]   第48/64个简盘操作符构建耗时: 0.1214 秒
[2025-07-24 21:25:19]   第49/64个简盘操作符构建耗时: 0.0840 秒
[2025-07-24 21:25:19]   第50/64个简盘操作符构建耗时: 0.1089 秒
[2025-07-24 21:25:19]   第51/64个简盘操作符构建耗时: 0.1090 秒
[2025-07-24 21:25:19]   第52/64个简盘操作符构建耗时: 0.1216 秒
[2025-07-24 21:25:19]   第53/64个简盘操作符构建耗时: 0.0841 秒
[2025-07-24 21:25:20]   第54/64个简盘操作符构建耗时: 0.1089 秒
[2025-07-24 21:25:20]   第55/64个简盘操作符构建耗时: 0.1077 秒
[2025-07-24 21:25:20]   第56/64个简盘操作符构建耗时: 0.1208 秒
[2025-07-24 21:25:20]   第57/64个简盘操作符构建耗时: 0.0840 秒
[2025-07-24 21:25:20]   第58/64个简盘操作符构建耗时: 0.1089 秒
[2025-07-24 21:25:20]   第59/64个简盘操作符构建耗时: 0.1088 秒
[2025-07-24 21:25:20]   第60/64个简盘操作符构建耗时: 0.1213 秒
[2025-07-24 21:25:20]   第61/64个简盘操作符构建耗时: 0.0839 秒
[2025-07-24 21:25:20]   第62/64个简盘操作符构建耗时: 0.1092 秒
[2025-07-24 21:25:21]   第63/64个简盘操作符构建耗时: 0.1388 秒
[2025-07-24 21:25:21]   第64/64个简盘操作符构建耗时: 0.1200 秒
[2025-07-24 21:25:21] 全部简盘操作符预计算总耗时: 8.0971 秒
[2025-07-24 21:25:21] 位移向量计算耗时: 0.0002 秒
[2025-07-24 21:25:21] 参考简盘操作符构建耗时: 0.0004 秒
[2025-07-24 21:25:21] 准备简盘相关函数计算...
[2025-07-24 21:25:21]   第1/64个算符构建耗时: 0.2456 秒
[2025-07-24 21:25:23]   第2/64个算符构建耗时: 2.5709 秒
[2025-07-24 21:25:25]   第3/64个算符构建耗时: 1.3318 秒
[2025-07-24 21:25:25]   第4/64个算符构建耗时: 0.3838 秒
[2025-07-24 21:25:26]   第5/64个算符构建耗时: 1.1754 秒
[2025-07-24 21:25:27]   第6/64个算符构建耗时: 0.4359 秒
[2025-07-24 21:25:27]   第7/64个算符构建耗时: 0.4370 秒
[2025-07-24 21:25:28]   第8/64个算符构建耗时: 0.4377 秒
[2025-07-24 21:25:28]   第9/64个算符构建耗时: 0.4377 秒
[2025-07-24 21:25:29]   第10/64个算符构建耗时: 0.4373 秒
[2025-07-24 21:25:29]   第11/64个算符构建耗时: 0.4370 秒
[2025-07-24 21:25:29]   第12/64个算符构建耗时: 0.4366 秒
[2025-07-24 21:25:30]   第13/64个算符构建耗时: 0.4368 秒
[2025-07-24 21:25:30]   第14/64个算符构建耗时: 0.4371 秒
[2025-07-24 21:25:31]   第15/64个算符构建耗时: 0.4426 秒
[2025-07-24 21:25:31]   第16/64个算符构建耗时: 0.4563 秒
[2025-07-24 21:25:32]   第17/64个算符构建耗时: 0.4379 秒
[2025-07-24 21:25:32]   第18/64个算符构建耗时: 0.4377 秒
[2025-07-24 21:25:33]   第19/64个算符构建耗时: 0.4381 秒
[2025-07-24 21:25:33]   第20/64个算符构建耗时: 0.4380 秒
[2025-07-24 21:25:33]   第21/64个算符构建耗时: 0.4380 秒
[2025-07-24 21:25:34]   第22/64个算符构建耗时: 0.4390 秒
[2025-07-24 21:25:34]   第23/64个算符构建耗时: 0.4388 秒
[2025-07-24 21:25:35]   第24/64个算符构建耗时: 0.4399 秒
[2025-07-24 21:25:35]   第25/64个算符构建耗时: 0.4389 秒
[2025-07-24 21:25:36]   第26/64个算符构建耗时: 0.4380 秒
[2025-07-24 21:25:36]   第27/64个算符构建耗时: 0.4397 秒
[2025-07-24 21:25:37]   第28/64个算符构建耗时: 0.4402 秒
[2025-07-24 21:25:37]   第29/64个算符构建耗时: 0.4409 秒
[2025-07-24 21:25:37]   第30/64个算符构建耗时: 0.4412 秒
[2025-07-24 21:25:38]   第31/64个算符构建耗时: 0.4414 秒
[2025-07-24 21:25:38]   第32/64个算符构建耗时: 0.4417 秒
[2025-07-24 21:25:39]   第33/64个算符构建耗时: 0.4417 秒
[2025-07-24 21:25:39]   第34/64个算符构建耗时: 0.4414 秒
[2025-07-24 21:25:40]   第35/64个算符构建耗时: 0.4408 秒
[2025-07-24 21:25:40]   第36/64个算符构建耗时: 0.4409 秒
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Resource Usage on 2025-07-24 21:39:46.530619:
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	JobId: 11396674.pbs101
	Project: personal-s240076
	Exit Status: 271
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	NCPUs: Requested(16), Used(16)
	CPU Time Used: 00:17:38
	Memory: Requested(110gb), Used(1663680kb)
	Vmem Used: 25676480kb
	Walltime: Requested(02:00:00), Used(00:17:50)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	Execution Nodes Used: (x1000c0s6b0n1:ngpus=1:ncpus=16:mem=115343360kb)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	GPU Duration: 17.88mins
	GPU Power Consumed: 227.42W
	GPU Max GPU Memory Used: 1.65GB
	Memory Throughput Rate (Average): x1000c0s6b0n1:(gpu0:34%)
	Memory Throughput Rate (Max): x1000c0s6b0n1:(gpu0:61%)
	Memory Throughput Rate (Min): x1000c0s6b0n1:(gpu0:0%)
	GPU SM Utilization (Average): x1000c0s6b0n1:(gpu0:86%)
	GPU SM Utilization (Max): x1000c0s6b0n1:(gpu0:100%)
	GPU SM Utilization (Min): x1000c0s6b0n1:(gpu0:0%)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Warning: None
GPU application profile: High
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

