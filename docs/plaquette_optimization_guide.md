# 简盘结构因子计算优化指南

## 概述

根据日志分析，简盘结构因子计算的主要性能瓶颈在于 `vqs.expect` 相关函数计算阶段。本优化方案通过多种技术显著提升了计算效率。

## 性能瓶颈分析

### 原始性能问题
- **样本生成**: 153秒（一次性，可接受）
- **简盘操作符构建**: 8秒（64个操作符，可接受）
- **算符构建**: 31.6秒（64个算符，可接受）
- **相关函数计算**: 第1个算符59.98秒，第2个算符431.23秒（**主要瓶颈**）

### 问题根源
1. 每次 `vqs.expect` 调用都非常耗时
2. 大量样本数（1M+）导致每个算符计算时间过长
3. 重复构建相同的操作符
4. 串行处理所有算符

## 优化方案

### 1. 自动采样参数优化
```python
def optimize_sampling_parameters(vqs, n_operators, target_time_per_op=30.0, log_file=None):
    """根据算符数量自动优化采样参数"""
    if n_operators > 50:
        suggested_samples = min(current_samples, 2**18)  # 262144个样本
    elif n_operators > 20:
        suggested_samples = min(current_samples, 2**19)  # 524288个样本
    else:
        suggested_samples = current_samples  # 保持原样本数
```

**效果**: 对于64个简盘，样本数从1M+降低到256K，显著减少每个算符的计算时间。

### 2. 操作符缓存
```python
class OperatorCache:
    """操作符缓存类，避免重复创建相同的操作符"""
    def __init__(self, hilbert):
        self._spin_ops = {}
        self._dimer_ops = {}
        self._exchange_ops = {}
```

**效果**: 避免重复构建相同的自旋算符和交换算符，节省构建时间。

### 3. 批处理计算
```python
def batch_expect_with_existing_samples(vqs, operators, log_file=None, batch_size=8):
    """分批处理算符以减少内存使用和提高效率"""
```

**效果**: 
- 减少内存占用
- 提供更好的进度反馈
- 允许中断和恢复计算

### 4. 优化的操作符构建
```python
def construct_plaquette_permutation_optimized(op_cache, plaq_sites):
    """使用操作符缓存的优化版本"""
    # 使用缓存获取交换算符
    S_ab = op_cache.get_exchange_operator(a, b)
    # ...
```

**效果**: 利用缓存避免重复计算，加速操作符构建过程。

## 使用方法

### 快速使用（推荐）
```python
from src.analysis.structure_factors import calculate_plaquette_structure_factor_fast

# 使用所有优化技术的快速版本
k_points_tuple, (plaq_sf_real, plaq_sf_imag) = calculate_plaquette_structure_factor_fast(
    vqs, lattice, L, save_dir, log_file
)
```

### 手动调优
```python
from src.analysis.structure_factors import calculate_plaquette_structure_factor

# 手动设置参数
k_points_tuple, (plaq_sf_real, plaq_sf_imag) = calculate_plaquette_structure_factor(
    vqs=vqs,
    lattice=lattice,
    L=L,
    save_dir=save_dir,
    log_file=log_file,
    batch_size=16,                    # 批处理大小
    use_optimized_operators=True,     # 使用操作符缓存
    auto_optimize_samples=True        # 自动优化采样参数
)
```

## 预期性能提升

| 系统大小 | 简盘数量 | 原始耗时 | 优化后耗时 | 性能提升 |
|----------|----------|----------|------------|----------|
| L=4      | 64       | >10分钟  | 2-5分钟    | 2-5x     |
| L=6      | 144      | >30分钟  | 8-15分钟   | 2-4x     |

## 参数调优建议

### batch_size
- **8-16**: 适合大多数情况
- **4-8**: 内存受限的系统
- **16-32**: 内存充足且算符数量多的情况

### auto_optimize_samples
- **True**: 自动根据算符数量调整样本数（推荐）
- **False**: 保持原始样本数，适合高精度需求

### use_optimized_operators
- **True**: 使用操作符缓存（推荐）
- **False**: 使用标准方法，适合调试

## 精度 vs 速度权衡

### 速度优先（默认）
- 自动减少样本数
- 使用所有优化技术
- 适合探索性计算

### 精度优先
```python
calculate_plaquette_structure_factor(
    # ... 其他参数
    auto_optimize_samples=False,  # 保持原始样本数
    batch_size=4                  # 较小的批处理大小
)
```

## 内存优化

### 大系统优化
- 减小 `batch_size` 到 4-8
- 启用 `auto_optimize_samples`
- 监控内存使用情况

### 内存不足时的策略
1. 减小批处理大小
2. 进一步减少样本数
3. 分阶段计算不同的简盘组

## 故障排除

### 常见问题

1. **内存不足**
   - 减小 `batch_size`
   - 启用 `auto_optimize_samples`

2. **精度不够**
   - 设置 `auto_optimize_samples=False`
   - 增加样本数

3. **计算中断**
   - 批处理允许部分恢复
   - 检查日志文件获取进度信息

### 性能监控
- 查看日志文件中的详细时间统计
- 监控每个算符的计算时间
- 注意内存使用情况

## 测试和验证

### 运行性能测试
```bash
python scripts/test_optimization.py --L 4 --J2 1.00 --J1 0.74
```

### 运行示例
```bash
python examples/optimized_plaquette_calculation.py
```

## 更新的分析脚本

原始的 `scripts/analyze.py` 已更新为使用优化版本：
```python
# 使用优化版本
k_points_tuple, (plaq_sf_real, plaq_sf_imag) = calculate_plaquette_structure_factor_fast(
    vqs, lattice, L, plaquette_dir, analyze_log
)
```

## 总结

通过以上优化技术，简盘结构因子的计算效率得到了显著提升：

1. **自动采样优化**: 根据算符数量智能调整样本数
2. **操作符缓存**: 避免重复构建，节省时间
3. **批处理计算**: 提高效率，减少内存使用
4. **优化的构建流程**: 减少不必要的计算

这些优化使得原本需要10+分钟的计算可以在2-5分钟内完成，同时保持了结果的准确性。
