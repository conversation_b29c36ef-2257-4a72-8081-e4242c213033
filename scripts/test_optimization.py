#!/usr/bin/env python3
"""
测试简盘结构因子计算的优化效果
比较原始版本和优化版本的性能
"""

import os
import sys
import time
import argparse
import numpy as np

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.logging import log_message
from src.models.gcnn import load_quantum_state
from src.analysis.structure_factors import (
    calculate_plaquette_structure_factor,
    calculate_plaquette_structure_factor_fast,
    generate_shared_samples,
)

def test_optimization_performance(L, J2, J1, test_dir):
    """
    测试优化前后的性能差异
    """
    log_file = os.path.join(test_dir, f"optimization_test_L={L}_J2={J2:.2f}_J1={J1:.2f}.log")
    
    log_message(log_file, "="*80)
    log_message(log_file, f"开始性能测试: L={L}, J2={J2:.2f}, J1={J1:.2f}")
    log_message(log_file, "="*80)
    
    # 加载量子态
    log_message(log_file, "加载量子态...")
    vqs, lattice = load_quantum_state(L, J2, J1)
    
    # 生成共享样本（两个版本都使用相同的样本）
    log_message(log_file, "生成共享样本...")
    original_samples = generate_shared_samples(vqs, n_samples=1048576, log_file=log_file)
    
    # 测试原始版本
    log_message(log_file, "\n" + "="*60)
    log_message(log_file, "测试原始版本（标准参数）...")
    log_message(log_file, "="*60)
    
    start_time = time.time()
    try:
        k_points_tuple_orig, (plaq_sf_real_orig, plaq_sf_imag_orig) = calculate_plaquette_structure_factor(
            vqs=vqs,
            lattice=lattice,
            L=L,
            save_dir=test_dir,
            log_file=log_file,
            batch_size=1,  # 原始的单个处理
            use_optimized_operators=False,
            auto_optimize_samples=False
        )
        orig_time = time.time() - start_time
        log_message(log_file, f"原始版本完成，总耗时: {orig_time:.2f} 秒")
        orig_success = True
    except Exception as e:
        orig_time = time.time() - start_time
        log_message(log_file, f"原始版本失败，耗时: {orig_time:.2f} 秒，错误: {str(e)}")
        orig_success = False
    
    # 重新生成样本（确保公平比较）
    log_message(log_file, "重新生成共享样本...")
    generate_shared_samples(vqs, n_samples=1048576, log_file=log_file)
    
    # 测试优化版本
    log_message(log_file, "\n" + "="*60)
    log_message(log_file, "测试优化版本（快速版本）...")
    log_message(log_file, "="*60)
    
    start_time = time.time()
    try:
        k_points_tuple_fast, (plaq_sf_real_fast, plaq_sf_imag_fast) = calculate_plaquette_structure_factor_fast(
            vqs=vqs,
            lattice=lattice,
            L=L,
            save_dir=test_dir,
            log_file=log_file
        )
        fast_time = time.time() - start_time
        log_message(log_file, f"优化版本完成，总耗时: {fast_time:.2f} 秒")
        fast_success = True
    except Exception as e:
        fast_time = time.time() - start_time
        log_message(log_file, f"优化版本失败，耗时: {fast_time:.2f} 秒，错误: {str(e)}")
        fast_success = False
    
    # 性能比较
    log_message(log_file, "\n" + "="*60)
    log_message(log_file, "性能比较结果")
    log_message(log_file, "="*60)
    
    if orig_success and fast_success:
        speedup = orig_time / fast_time
        log_message(log_file, f"原始版本耗时: {orig_time:.2f} 秒")
        log_message(log_file, f"优化版本耗时: {fast_time:.2f} 秒")
        log_message(log_file, f"性能提升倍数: {speedup:.2f}x")
        log_message(log_file, f"时间节省: {orig_time - fast_time:.2f} 秒 ({(1-fast_time/orig_time)*100:.1f}%)")
        
        # 结果一致性检查
        if np.allclose(plaq_sf_real_orig, plaq_sf_real_fast, rtol=1e-10, atol=1e-10):
            log_message(log_file, "✓ 结果一致性检查通过")
        else:
            max_diff = np.max(np.abs(plaq_sf_real_orig - plaq_sf_real_fast))
            log_message(log_file, f"⚠ 结果存在差异，最大差异: {max_diff:.2e}")
    
    elif fast_success:
        log_message(log_file, f"原始版本失败，优化版本成功，耗时: {fast_time:.2f} 秒")
        log_message(log_file, "✓ 优化版本提供了更好的稳定性")
    
    elif orig_success:
        log_message(log_file, f"原始版本成功，耗时: {orig_time:.2f} 秒，优化版本失败")
        log_message(log_file, "⚠ 需要进一步调试优化版本")
    
    else:
        log_message(log_file, "两个版本都失败了")
        log_message(log_file, "⚠ 需要检查基础设置")
    
    # 恢复原始样本数
    if original_samples is not None:
        vqs.n_samples = original_samples
    
    log_message(log_file, "\n性能测试完成")
    return orig_time if orig_success else None, fast_time if fast_success else None

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='测试简盘结构因子计算优化效果')
    parser.add_argument('--L', type=int, default=4, help='晶格大小')
    parser.add_argument('--J2', type=float, default=1.00, help='J2耦合强度')
    parser.add_argument('--J1', type=float, default=0.74, help='J1耦合强度')
    args = parser.parse_args()
    
    # 创建测试目录
    test_dir = f"results/L={args.L}/J2={args.J2:.2f}/J1={args.J1:.2f}/optimization_test"
    os.makedirs(test_dir, exist_ok=True)
    
    # 运行性能测试
    orig_time, fast_time = test_optimization_performance(args.L, args.J2, args.J1, test_dir)
    
    # 输出总结
    print("\n" + "="*60)
    print("性能测试总结")
    print("="*60)
    
    if orig_time and fast_time:
        speedup = orig_time / fast_time
        print(f"原始版本: {orig_time:.2f} 秒")
        print(f"优化版本: {fast_time:.2f} 秒")
        print(f"性能提升: {speedup:.2f}x")
        print(f"时间节省: {orig_time - fast_time:.2f} 秒")
    elif fast_time:
        print(f"优化版本成功: {fast_time:.2f} 秒")
        print("原始版本失败")
    elif orig_time:
        print(f"原始版本: {orig_time:.2f} 秒")
        print("优化版本失败")
    else:
        print("两个版本都失败")
    
    print(f"\n详细日志保存在: {test_dir}")

if __name__ == "__main__":
    main()
