#!/usr/bin/env python3
"""
优化后的简盘结构因子计算示例
展示如何使用新的优化功能来显著提升计算速度
"""

import os
import sys
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.logging import log_message
from src.models.gcnn import load_quantum_state
from src.analysis.structure_factors import (
    calculate_plaquette_structure_factor_fast,
    calculate_plaquette_structure_factor,
    generate_shared_samples,
)

def example_fast_calculation():
    """
    示例：使用优化版本快速计算简盘结构因子
    """
    # 参数设置
    L = 4
    J2 = 1.00
    J1 = 0.74
    
    # 创建结果目录
    result_dir = f"examples/results/L={L}/J2={J2:.2f}/J1={J1:.2f}"
    os.makedirs(result_dir, exist_ok=True)
    
    log_file = os.path.join(result_dir, "fast_calculation.log")
    
    print("="*60)
    print("优化版本简盘结构因子计算示例")
    print("="*60)
    print(f"参数: L={L}, J2={J2:.2f}, J1={J1:.2f}")
    print(f"结果保存到: {result_dir}")
    print()
    
    # 1. 加载量子态
    print("1. 加载量子态...")
    log_message(log_file, "开始加载量子态...")
    vqs, lattice = load_quantum_state(L, J2, J1)
    log_message(log_file, "量子态加载完成")
    
    # 2. 生成共享样本
    print("2. 生成共享样本...")
    log_message(log_file, "生成共享样本...")
    generate_shared_samples(vqs, n_samples=1048576, log_file=log_file)
    
    # 3. 使用快速版本计算
    print("3. 使用优化版本计算简盘结构因子...")
    start_time = time.time()
    
    k_points_tuple, (plaq_sf_real, plaq_sf_imag) = calculate_plaquette_structure_factor_fast(
        vqs=vqs,
        lattice=lattice,
        L=L,
        save_dir=result_dir,
        log_file=log_file
    )
    
    total_time = time.time() - start_time
    
    print(f"✓ 计算完成！总耗时: {total_time:.2f} 秒")
    print(f"✓ 结果已保存到: {result_dir}/plaquette_data.npy")
    
    # 4. 显示结果统计
    print("\n结果统计:")
    print(f"  - 结构因子最大值: {plaq_sf_real.max():.6f}")
    print(f"  - 结构因子最小值: {plaq_sf_real.min():.6f}")
    print(f"  - 结构因子平均值: {plaq_sf_real.mean():.6f}")
    
    log_message(log_file, f"计算完成，总耗时: {total_time:.2f} 秒")
    log_message(log_file, f"结构因子统计 - 最大值: {plaq_sf_real.max():.6f}, 最小值: {plaq_sf_real.min():.6f}, 平均值: {plaq_sf_real.mean():.6f}")
    
    return total_time

def example_parameter_comparison():
    """
    示例：比较不同参数设置的性能
    """
    L = 4
    J2 = 1.00
    J1 = 0.74
    
    result_dir = f"examples/results/L={L}/J2={J2:.2f}/J1={J1:.2f}/parameter_comparison"
    os.makedirs(result_dir, exist_ok=True)
    
    log_file = os.path.join(result_dir, "parameter_comparison.log")
    
    print("\n" + "="*60)
    print("参数设置性能比较示例")
    print("="*60)
    
    # 加载量子态
    vqs, lattice = load_quantum_state(L, J2, J1)
    
    # 测试不同的参数组合
    test_configs = [
        {
            "name": "默认优化",
            "params": {
                "batch_size": 16,
                "use_optimized_operators": True,
                "auto_optimize_samples": True
            }
        },
        {
            "name": "小批处理",
            "params": {
                "batch_size": 4,
                "use_optimized_operators": True,
                "auto_optimize_samples": True
            }
        },
        {
            "name": "无自动优化",
            "params": {
                "batch_size": 8,
                "use_optimized_operators": True,
                "auto_optimize_samples": False
            }
        },
        {
            "name": "标准操作符",
            "params": {
                "batch_size": 8,
                "use_optimized_operators": False,
                "auto_optimize_samples": True
            }
        }
    ]
    
    results = []
    
    for config in test_configs:
        print(f"\n测试配置: {config['name']}")
        log_message(log_file, f"测试配置: {config['name']}, 参数: {config['params']}")
        
        # 重新生成样本确保公平比较
        generate_shared_samples(vqs, n_samples=1048576, log_file=log_file)
        
        start_time = time.time()
        try:
            k_points_tuple, (plaq_sf_real, plaq_sf_imag) = calculate_plaquette_structure_factor(
                vqs=vqs,
                lattice=lattice,
                L=L,
                save_dir=result_dir,
                log_file=log_file,
                **config['params']
            )
            elapsed_time = time.time() - start_time
            print(f"  ✓ 完成，耗时: {elapsed_time:.2f} 秒")
            results.append((config['name'], elapsed_time, True))
            log_message(log_file, f"配置 {config['name']} 完成，耗时: {elapsed_time:.2f} 秒")
        except Exception as e:
            elapsed_time = time.time() - start_time
            print(f"  ✗ 失败，耗时: {elapsed_time:.2f} 秒，错误: {str(e)}")
            results.append((config['name'], elapsed_time, False))
            log_message(log_file, f"配置 {config['name']} 失败，耗时: {elapsed_time:.2f} 秒，错误: {str(e)}")
    
    # 显示比较结果
    print("\n" + "="*60)
    print("性能比较结果")
    print("="*60)
    
    successful_results = [(name, time_taken) for name, time_taken, success in results if success]
    
    if successful_results:
        # 按时间排序
        successful_results.sort(key=lambda x: x[1])
        
        print("配置排名（按速度）:")
        for i, (name, time_taken) in enumerate(successful_results, 1):
            print(f"  {i}. {name}: {time_taken:.2f} 秒")
        
        # 计算相对性能
        fastest_time = successful_results[0][1]
        print(f"\n相对于最快配置的性能:")
        for name, time_taken in successful_results:
            relative_speed = time_taken / fastest_time
            print(f"  {name}: {relative_speed:.2f}x")
    
    print(f"\n详细日志保存在: {log_file}")

def main():
    """主函数"""
    print("简盘结构因子计算优化示例")
    print("本示例展示如何使用优化后的函数来显著提升计算速度")
    
    # 示例1：快速计算
    fast_time = example_fast_calculation()
    
    # 示例2：参数比较
    example_parameter_comparison()
    
    print("\n" + "="*60)
    print("示例完成")
    print("="*60)
    print(f"快速计算耗时: {fast_time:.2f} 秒")
    print("\n主要优化技术:")
    print("  1. 自动采样参数优化 - 根据算符数量调整样本数")
    print("  2. 操作符缓存 - 避免重复构建相同操作符")
    print("  3. 批处理计算 - 分批处理提高效率")
    print("  4. 减少日志输出 - 降低I/O开销")
    print("\n建议:")
    print("  - 对于日常计算，使用 calculate_plaquette_structure_factor_fast()")
    print("  - 对于高精度需求，可以手动调整参数")
    print("  - 对于大系统，适当减小 batch_size")

if __name__ == "__main__":
    main()
